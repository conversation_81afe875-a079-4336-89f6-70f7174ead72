package com.shipment.erp.controller;

import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.shipment.erp.ShipmentManagementApp;
import com.shipment.erp.config.DatabaseConfig;
import com.shipment.erp.service.UserService;
import com.shipment.erp.util.AlertUtil;

import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ResourceBundle;

/**
 * تحكم شاشة تسجيل الدخول
 * Login Screen Controller
 * 
 * يدير عملية تسجيل الدخول والتحقق من المستخدمين
 * Manages login process and user authentication
 * 
 * <AUTHOR> إدارة الشحنات
 * @version 1.0.0
 */
public class LoginController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);
    
    // عناصر واجهة المستخدم
    // UI Components
    @FXML private TextField usernameField;
    @FXML private PasswordField passwordField;
    @FXML private CheckBox rememberMeCheckBox;
    @FXML private Label errorLabel;
    @FXML private Button loginButton;
    @FXML private Button cancelButton;
    @FXML private Label versionLabel;
    @FXML private Label dbStatusLabel;
    
    // الخدمات
    // Services
    private UserService userService;
    private ResourceBundle resourceBundle;
    
    // متغيرات التحكم
    // Control Variables
    private int failedAttempts = 0;
    private static final int MAX_FAILED_ATTEMPTS = 3;
    private static final String DEFAULT_USERNAME = "admin";
    private static final String DEFAULT_PASSWORD_HASH = "ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f"; // admin123
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        this.resourceBundle = resources;
        
        logger.info("تهيئة شاشة تسجيل الدخول - Initializing login screen");
        
        // تهيئة الخدمات
        // Initialize services
        initializeServices();
        
        // إعداد واجهة المستخدم
        // Setup UI
        setupUI();
        
        // إعداد معالجات الأحداث
        // Setup event handlers
        setupEventHandlers();
        
        // التحقق من حالة قاعدة البيانات
        // Check database status
        checkDatabaseStatus();
        
        logger.info("تم تهيئة شاشة تسجيل الدخول بنجاح - Login screen initialized successfully");
    }
    
    /**
     * تهيئة الخدمات
     * Initialize services
     */
    private void initializeServices() {
        try {
            userService = new UserService();
            logger.info("تم تهيئة خدمات المستخدم - User services initialized");
        } catch (Exception e) {
            logger.error("فشل في تهيئة خدمات المستخدم - Failed to initialize user services", e);
            showError("خطأ في النظام", "فشل في تهيئة خدمات النظام");
        }
    }
    
    /**
     * إعداد واجهة المستخدم
     * Setup user interface
     */
    private void setupUI() {
        // تعيين النص الافتراضي
        // Set default text
        if (versionLabel != null) {
            versionLabel.setText("الإصدار 1.0.0");
        }
        
        // تركيز على حقل اسم المستخدم
        // Focus on username field
        Platform.runLater(() -> {
            if (usernameField != null) {
                usernameField.requestFocus();
            }
        });
        
        // إخفاء رسالة الخطأ
        // Hide error message
        if (errorLabel != null) {
            errorLabel.setVisible(false);
        }
        
        // تعيين قيم افتراضية للتطوير
        // Set default values for development
        if (usernameField != null) {
            usernameField.setText("admin");
        }
        if (passwordField != null) {
            passwordField.setText("admin123");
        }
    }
    
    /**
     * إعداد معالجات الأحداث
     * Setup event handlers
     */
    private void setupEventHandlers() {
        // معالج الضغط على Enter
        // Enter key handler
        if (usernameField != null) {
            usernameField.setOnKeyPressed(this::handleKeyPressed);
        }
        if (passwordField != null) {
            passwordField.setOnKeyPressed(this::handleKeyPressed);
        }
        
        // معالج تغيير النص لإخفاء رسالة الخطأ
        // Text change handler to hide error message
        if (usernameField != null) {
            usernameField.textProperty().addListener((obs, oldText, newText) -> hideError());
        }
        if (passwordField != null) {
            passwordField.textProperty().addListener((obs, oldText, newText) -> hideError());
        }
    }
    
    /**
     * معالج الضغط على المفاتيح
     * Key press handler
     */
    private void handleKeyPressed(KeyEvent event) {
        if (event.getCode() == KeyCode.ENTER) {
            handleLogin();
        } else if (event.getCode() == KeyCode.ESCAPE) {
            handleCancel();
        }
    }
    
    /**
     * معالج تسجيل الدخول
     * Login handler
     */
    @FXML
    private void handleLogin() {
        logger.info("محاولة تسجيل دخول - Login attempt");
        
        // التحقق من صحة البيانات
        // Validate input
        if (!validateInput()) {
            return;
        }
        
        String username = usernameField.getText().trim();
        String password = passwordField.getText();
        
        // تعطيل الأزرار أثناء المعالجة
        // Disable buttons during processing
        setButtonsEnabled(false);
        
        // إنشاء مهمة تسجيل الدخول
        // Create login task
        Task<Boolean> loginTask = new Task<Boolean>() {
            @Override
            protected Boolean call() throws Exception {
                return authenticateUser(username, password);
            }
            
            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    setButtonsEnabled(true);
                    if (getValue()) {
                        handleSuccessfulLogin(username);
                    } else {
                        handleFailedLogin();
                    }
                });
            }
            
            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    setButtonsEnabled(true);
                    logger.error("خطأ في تسجيل الدخول - Login error", getException());
                    showError("خطأ في تسجيل الدخول", "حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.");
                });
            }
        };
        
        // تشغيل المهمة في خيط منفصل
        // Run task in separate thread
        Thread loginThread = new Thread(loginTask);
        loginThread.setDaemon(true);
        loginThread.start();
    }
    
    /**
     * التحقق من صحة البيانات المدخلة
     * Validate input data
     */
    private boolean validateInput() {
        String username = usernameField.getText().trim();
        String password = passwordField.getText();
        
        if (username.isEmpty()) {
            showError("خطأ في البيانات", "يرجى إدخال اسم المستخدم");
            usernameField.requestFocus();
            return false;
        }
        
        if (password.isEmpty()) {
            showError("خطأ في البيانات", "يرجى إدخال كلمة المرور");
            passwordField.requestFocus();
            return false;
        }
        
        if (username.length() < 3) {
            showError("خطأ في البيانات", "اسم المستخدم يجب أن يكون 3 أحرف على الأقل");
            usernameField.requestFocus();
            return false;
        }
        
        if (password.length() < 6) {
            showError("خطأ في البيانات", "كلمة المرور يجب أن تكون 6 أحرف على الأقل");
            passwordField.requestFocus();
            return false;
        }
        
        return true;
    }
    
    /**
     * التحقق من صحة المستخدم
     * Authenticate user
     */
    private boolean authenticateUser(String username, String password) {
        try {
            // تشفير كلمة المرور
            // Hash password
            String hashedPassword = hashPassword(password);
            
            // التحقق من المستخدم الافتراضي
            // Check default user
            if (DEFAULT_USERNAME.equals(username) && DEFAULT_PASSWORD_HASH.equals(hashedPassword)) {
                logger.info("تم تسجيل دخول المستخدم الافتراضي - Default user logged in");
                return true;
            }
            
            // التحقق من قاعدة البيانات
            // Check database
            if (userService != null) {
                return userService.authenticateUser(username, hashedPassword);
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("خطأ في التحقق من المستخدم - Error authenticating user", e);
            return false;
        }
    }
    
    /**
     * تشفير كلمة المرور
     * Hash password
     */
    private String hashPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashedBytes = md.digest(password.getBytes());
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hashedBytes) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            logger.error("خطأ في تشفير كلمة المرور - Error hashing password", e);
            throw new RuntimeException("Failed to hash password", e);
        }
    }
    
    /**
     * معالج تسجيل الدخول الناجح
     * Handle successful login
     */
    private void handleSuccessfulLogin(String username) {
        logger.info("تم تسجيل الدخول بنجاح للمستخدم: {} - Successful login for user: {}", username, username);
        
        // إعادة تعيين عداد المحاولات الفاشلة
        // Reset failed attempts counter
        failedAttempts = 0;
        
        // حفظ معلومات المستخدم إذا كان مطلوباً
        // Save user information if remember me is checked
        if (rememberMeCheckBox.isSelected()) {
            // TODO: حفظ معلومات المستخدم
            logger.info("تم حفظ معلومات المستخدم - User information saved");
        }
        
        // الانتقال إلى الشاشة الرئيسية
        // Navigate to main screen
        try {
            ShipmentManagementApp app = (ShipmentManagementApp) usernameField.getScene().getWindow().getUserData();
            if (app != null) {
                app.showMainScreen();
            } else {
                // إنشاء نافذة رئيسية جديدة
                // Create new main window
                logger.info("إنشاء نافذة رئيسية جديدة - Creating new main window");
                // TODO: إنشاء النافذة الرئيسية
            }
        } catch (Exception e) {
            logger.error("خطأ في الانتقال إلى الشاشة الرئيسية - Error navigating to main screen", e);
            showError("خطأ في النظام", "حدث خطأ أثناء فتح الشاشة الرئيسية");
        }
    }
    
    /**
     * معالج تسجيل الدخول الفاشل
     * Handle failed login
     */
    private void handleFailedLogin() {
        failedAttempts++;
        logger.warn("محاولة تسجيل دخول فاشلة رقم: {} - Failed login attempt: {}", failedAttempts, failedAttempts);
        
        if (failedAttempts >= MAX_FAILED_ATTEMPTS) {
            showError("تم تجاوز الحد الأقصى", 
                String.format("تم تجاوز الحد الأقصى للمحاولات (%d). يرجى المحاولة لاحقاً.", MAX_FAILED_ATTEMPTS));
            
            // تعطيل الأزرار لفترة
            // Disable buttons temporarily
            setButtonsEnabled(false);
            
            // إعادة تفعيل الأزرار بعد 30 ثانية
            // Re-enable buttons after 30 seconds
            Task<Void> delayTask = new Task<Void>() {
                @Override
                protected Void call() throws Exception {
                    Thread.sleep(30000); // 30 seconds
                    return null;
                }
                
                @Override
                protected void succeeded() {
                    Platform.runLater(() -> {
                        setButtonsEnabled(true);
                        failedAttempts = 0;
                        hideError();
                    });
                }
            };
            
            Thread delayThread = new Thread(delayTask);
            delayThread.setDaemon(true);
            delayThread.start();
            
        } else {
            showError("خطأ في تسجيل الدخول", 
                String.format("اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: %d", 
                    MAX_FAILED_ATTEMPTS - failedAttempts));
            
            // مسح كلمة المرور
            // Clear password
            passwordField.clear();
            passwordField.requestFocus();
        }
    }
    
    /**
     * معالج نسيان كلمة المرور
     * Forgot password handler
     */
    @FXML
    private void handleForgotPassword() {
        logger.info("طلب استعادة كلمة المرور - Password recovery requested");
        
        AlertUtil.showInfo("استعادة كلمة المرور", 
            "يرجى الاتصال بمدير النظام لاستعادة كلمة المرور.\n\nالهاتف: +20-2-12345678\nالبريد الإلكتروني: <EMAIL>");
    }
    
    /**
     * معالج الإلغاء
     * Cancel handler
     */
    @FXML
    private void handleCancel() {
        logger.info("إلغاء تسجيل الدخول - Login cancelled");
        
        boolean confirmed = AlertUtil.showConfirmation("تأكيد الخروج", "هل أنت متأكد من الخروج من النظام؟");
        if (confirmed) {
            Platform.exit();
        }
    }
    
    /**
     * التحقق من حالة قاعدة البيانات
     * Check database status
     */
    private void checkDatabaseStatus() {
        Task<Boolean> dbCheckTask = new Task<Boolean>() {
            @Override
            protected Boolean call() throws Exception {
                return DatabaseConfig.getInstance().isConnectionValid();
            }
            
            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    boolean connected = getValue();
                    if (dbStatusLabel != null) {
                        if (connected) {
                            dbStatusLabel.setText("متصل بقاعدة البيانات");
                            dbStatusLabel.getStyleClass().removeAll("db-disconnected");
                            dbStatusLabel.getStyleClass().add("db-connected");
                        } else {
                            dbStatusLabel.setText("غير متصل بقاعدة البيانات");
                            dbStatusLabel.getStyleClass().removeAll("db-connected");
                            dbStatusLabel.getStyleClass().add("db-disconnected");
                        }
                    }
                });
            }
            
            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    if (dbStatusLabel != null) {
                        dbStatusLabel.setText("خطأ في الاتصال بقاعدة البيانات");
                        dbStatusLabel.getStyleClass().removeAll("db-connected");
                        dbStatusLabel.getStyleClass().add("db-error");
                    }
                });
            }
        };
        
        Thread dbCheckThread = new Thread(dbCheckTask);
        dbCheckThread.setDaemon(true);
        dbCheckThread.start();
    }
    
    /**
     * تفعيل/تعطيل الأزرار
     * Enable/disable buttons
     */
    private void setButtonsEnabled(boolean enabled) {
        if (loginButton != null) {
            loginButton.setDisable(!enabled);
        }
        if (cancelButton != null) {
            cancelButton.setDisable(!enabled);
        }
        if (usernameField != null) {
            usernameField.setDisable(!enabled);
        }
        if (passwordField != null) {
            passwordField.setDisable(!enabled);
        }
    }
    
    /**
     * عرض رسالة خطأ
     * Show error message
     */
    private void showError(String title, String message) {
        if (errorLabel != null) {
            errorLabel.setText(message);
            errorLabel.setVisible(true);
        }
        logger.warn("رسالة خطأ: {} - {}", title, message);
    }
    
    /**
     * إخفاء رسالة الخطأ
     * Hide error message
     */
    private void hideError() {
        if (errorLabel != null) {
            errorLabel.setVisible(false);
        }
    }
}
