package com.shipment.erp.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.net.URL;
import java.util.Locale;
import java.util.ResourceBundle;

/**
 * أدوات الموارد والملفات
 * Resource and File Utilities
 * 
 * يوفر طرق سهلة للوصول إلى الموارد والملفات مع دعم اللغة العربية
 * Provides easy methods for accessing resources and files with Arabic support
 * 
 * <AUTHOR> إدارة الشحنات
 * @version 1.0.0
 */
public class ResourceUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(ResourceUtil.class);
    
    // منع إنشاء مثيل من الفئة
    // Prevent instantiation
    private ResourceUtil() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    /**
     * تحميل ملف الترجمة
     * Load resource bundle
     */
    public static ResourceBundle loadResourceBundle(String baseName) {
        return loadResourceBundle(baseName, Locale.getDefault());
    }
    
    /**
     * تحميل ملف الترجمة مع لغة محددة
     * Load resource bundle with specific locale
     */
    public static ResourceBundle loadResourceBundle(String baseName, Locale locale) {
        try {
            ResourceBundle bundle = ResourceBundle.getBundle(baseName, locale);
            logger.info("تم تحميل ملف الترجمة: {} للغة: {}", baseName, locale);
            return bundle;
        } catch (Exception e) {
            logger.error("فشل في تحميل ملف الترجمة: {} للغة: {}", baseName, locale, e);
            // العودة للإنجليزية كافتراضي
            // Fallback to English
            return ResourceBundle.getBundle(baseName, Locale.ENGLISH);
        }
    }
    
    /**
     * الحصول على نص مترجم
     * Get translated text
     */
    public static String getString(ResourceBundle bundle, String key) {
        return getString(bundle, key, key);
    }
    
    /**
     * الحصول على نص مترجم مع قيمة افتراضية
     * Get translated text with default value
     */
    public static String getString(ResourceBundle bundle, String key, String defaultValue) {
        try {
            return bundle.getString(key);
        } catch (Exception e) {
            logger.warn("تعذر العثور على المفتاح: {} في ملف الترجمة", key);
            return defaultValue;
        }
    }
    
    /**
     * تحميل ملف من الموارد
     * Load file from resources
     */
    public static InputStream getResourceAsStream(String path) {
        try {
            InputStream stream = ResourceUtil.class.getResourceAsStream(path);
            if (stream == null) {
                logger.warn("تعذر العثور على الملف: {}", path);
            } else {
                logger.debug("تم تحميل الملف: {}", path);
            }
            return stream;
        } catch (Exception e) {
            logger.error("خطأ في تحميل الملف: {}", path, e);
            return null;
        }
    }
    
    /**
     * الحصول على رابط ملف من الموارد
     * Get resource URL
     */
    public static URL getResource(String path) {
        try {
            URL url = ResourceUtil.class.getResource(path);
            if (url == null) {
                logger.warn("تعذر العثور على الملف: {}", path);
            } else {
                logger.debug("تم العثور على الملف: {}", path);
            }
            return url;
        } catch (Exception e) {
            logger.error("خطأ في الحصول على رابط الملف: {}", path, e);
            return null;
        }
    }
    
    /**
     * التحقق من وجود ملف في الموارد
     * Check if resource exists
     */
    public static boolean resourceExists(String path) {
        return getResource(path) != null;
    }
    
    /**
     * تحميل ملف CSS
     * Load CSS file
     */
    public static String loadCSS(String cssFileName) {
        String path = "/css/" + cssFileName;
        URL cssUrl = getResource(path);
        
        if (cssUrl != null) {
            logger.debug("تم تحميل ملف CSS: {}", cssFileName);
            return cssUrl.toExternalForm();
        } else {
            logger.warn("تعذر تحميل ملف CSS: {}", cssFileName);
            return null;
        }
    }
    
    /**
     * تحميل ملف FXML
     * Load FXML file
     */
    public static URL loadFXML(String fxmlFileName) {
        String path = "/fxml/" + fxmlFileName;
        URL fxmlUrl = getResource(path);
        
        if (fxmlUrl != null) {
            logger.debug("تم تحميل ملف FXML: {}", fxmlFileName);
        } else {
            logger.warn("تعذر تحميل ملف FXML: {}", fxmlFileName);
        }
        
        return fxmlUrl;
    }
    
    /**
     * تحميل صورة
     * Load image
     */
    public static InputStream loadImage(String imageFileName) {
        String path = "/images/" + imageFileName;
        InputStream imageStream = getResourceAsStream(path);
        
        if (imageStream != null) {
            logger.debug("تم تحميل الصورة: {}", imageFileName);
        } else {
            logger.warn("تعذر تحميل الصورة: {}", imageFileName);
        }
        
        return imageStream;
    }
    
    /**
     * تحميل أيقونة
     * Load icon
     */
    public static InputStream loadIcon(String iconFileName) {
        String path = "/icons/" + iconFileName;
        InputStream iconStream = getResourceAsStream(path);
        
        if (iconStream != null) {
            logger.debug("تم تحميل الأيقونة: {}", iconFileName);
        } else {
            logger.warn("تعذر تحميل الأيقونة: {}", iconFileName);
        }
        
        return iconStream;
    }
    
    /**
     * تحميل ملف صوتي
     * Load audio file
     */
    public static URL loadAudio(String audioFileName) {
        String path = "/audio/" + audioFileName;
        URL audioUrl = getResource(path);
        
        if (audioUrl != null) {
            logger.debug("تم تحميل الملف الصوتي: {}", audioFileName);
        } else {
            logger.warn("تعذر تحميل الملف الصوتي: {}", audioFileName);
        }
        
        return audioUrl;
    }
    
    /**
     * تحميل ملف خط
     * Load font file
     */
    public static InputStream loadFont(String fontFileName) {
        String path = "/fonts/" + fontFileName;
        InputStream fontStream = getResourceAsStream(path);
        
        if (fontStream != null) {
            logger.debug("تم تحميل الخط: {}", fontFileName);
        } else {
            logger.warn("تعذر تحميل الخط: {}", fontFileName);
        }
        
        return fontStream;
    }
    
    /**
     * تحميل ملف تقرير
     * Load report file
     */
    public static InputStream loadReport(String reportFileName) {
        String path = "/reports/" + reportFileName;
        InputStream reportStream = getResourceAsStream(path);
        
        if (reportStream != null) {
            logger.debug("تم تحميل التقرير: {}", reportFileName);
        } else {
            logger.warn("تعذر تحميل التقرير: {}", reportFileName);
        }
        
        return reportStream;
    }
    
    /**
     * تحميل ملف قالب
     * Load template file
     */
    public static InputStream loadTemplate(String templateFileName) {
        String path = "/templates/" + templateFileName;
        InputStream templateStream = getResourceAsStream(path);
        
        if (templateStream != null) {
            logger.debug("تم تحميل القالب: {}", templateFileName);
        } else {
            logger.warn("تعذر تحميل القالب: {}", templateFileName);
        }
        
        return templateStream;
    }
    
    /**
     * الحصول على مسار مجلد الموارد
     * Get resources directory path
     */
    public static String getResourcesPath() {
        try {
            URL resourceUrl = ResourceUtil.class.getResource("/");
            if (resourceUrl != null) {
                return resourceUrl.getPath();
            }
        } catch (Exception e) {
            logger.error("خطأ في الحصول على مسار الموارد", e);
        }
        return null;
    }
    
    /**
     * تحميل ملف إعدادات
     * Load configuration file
     */
    public static InputStream loadConfig(String configFileName) {
        InputStream configStream = getResourceAsStream("/" + configFileName);
        
        if (configStream != null) {
            logger.debug("تم تحميل ملف الإعدادات: {}", configFileName);
        } else {
            logger.warn("تعذر تحميل ملف الإعدادات: {}", configFileName);
        }
        
        return configStream;
    }
}
