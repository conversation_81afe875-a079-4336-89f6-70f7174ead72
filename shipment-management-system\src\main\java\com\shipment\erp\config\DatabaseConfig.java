package com.shipment.erp.config;

import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

/**
 * إعدادات قاعدة البيانات
 * Database Configuration Class
 * 
 * يدير اتصالات قاعدة البيانات Oracle مع دعم الترميز العربي
 * Manages Oracle database connections with Arabic encoding support
 * 
 * <AUTHOR> إدارة الشحنات
 * @version 1.0.0
 */
public class DatabaseConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseConfig.class);
    
    private static DatabaseConfig instance;
    private HikariDataSource dataSource;
    private Properties dbProperties;
    
    // إعدادات افتراضية
    // Default settings
    private static final String DEFAULT_PROPERTIES_FILE = "database.properties";
    private static final String DRIVER_CLASS_NAME = "oracle.jdbc.OracleDriver";
    
    /**
     * منشئ خاص للنمط Singleton
     * Private constructor for Singleton pattern
     */
    private DatabaseConfig() {
        loadProperties();
    }
    
    /**
     * الحصول على مثيل وحيد من الفئة
     * Get singleton instance
     */
    public static synchronized DatabaseConfig getInstance() {
        if (instance == null) {
            instance = new DatabaseConfig();
        }
        return instance;
    }
    
    /**
     * تحميل إعدادات قاعدة البيانات
     * Load database properties
     */
    private void loadProperties() {
        dbProperties = new Properties();
        
        try (InputStream input = getClass().getClassLoader().getResourceAsStream(DEFAULT_PROPERTIES_FILE)) {
            if (input == null) {
                logger.error("تعذر العثور على ملف الإعدادات: {}", DEFAULT_PROPERTIES_FILE);
                throw new RuntimeException("Database properties file not found: " + DEFAULT_PROPERTIES_FILE);
            }
            
            dbProperties.load(input);
            logger.info("تم تحميل إعدادات قاعدة البيانات بنجاح");
            
        } catch (IOException e) {
            logger.error("خطأ في تحميل إعدادات قاعدة البيانات", e);
            throw new RuntimeException("Failed to load database properties", e);
        }
    }
    
    /**
     * تهيئة مجموعة الاتصالات
     * Initialize connection pool
     */
    public void initialize() {
        try {
            // تحميل تعريف Oracle JDBC
            // Load Oracle JDBC driver
            Class.forName(DRIVER_CLASS_NAME);
            
            // إعداد HikariCP
            // Setup HikariCP
            HikariConfig config = new HikariConfig();
            
            // إعدادات الاتصال الأساسية
            // Basic connection settings
            config.setJdbcUrl(dbProperties.getProperty("db.url"));
            config.setUsername(dbProperties.getProperty("db.username"));
            config.setPassword(dbProperties.getProperty("db.password"));
            config.setDriverClassName(DRIVER_CLASS_NAME);
            
            // إعدادات مجموعة الاتصالات
            // Connection pool settings
            config.setMinimumIdle(Integer.parseInt(dbProperties.getProperty("db.pool.minimumIdle", "5")));
            config.setMaximumPoolSize(Integer.parseInt(dbProperties.getProperty("db.pool.maximumPoolSize", "20")));
            config.setConnectionTimeout(Long.parseLong(dbProperties.getProperty("db.pool.connectionTimeout", "30000")));
            config.setIdleTimeout(Long.parseLong(dbProperties.getProperty("db.pool.idleTimeout", "600000")));
            config.setMaxLifetime(Long.parseLong(dbProperties.getProperty("db.pool.maxLifetime", "1800000")));
            config.setLeakDetectionThreshold(Long.parseLong(dbProperties.getProperty("db.pool.leakDetectionThreshold", "60000")));
            
            // إعدادات الأداء
            // Performance settings
            config.addDataSourceProperty("cachePrepStmts", dbProperties.getProperty("db.pool.cachePrepStmts", "true"));
            config.addDataSourceProperty("prepStmtCacheSize", dbProperties.getProperty("db.pool.prepStmtCacheSize", "250"));
            config.addDataSourceProperty("prepStmtCacheSqlLimit", dbProperties.getProperty("db.pool.prepStmtCacheSqlLimit", "2048"));
            config.addDataSourceProperty("useServerPrepStmts", dbProperties.getProperty("db.pool.useServerPrepStmts", "true"));
            
            // إعدادات Oracle محددة
            // Oracle specific settings
            config.addDataSourceProperty("oracle.net.CONNECT_TIMEOUT", dbProperties.getProperty("oracle.net.CONNECT_TIMEOUT", "10000"));
            config.addDataSourceProperty("oracle.jdbc.ReadTimeout", dbProperties.getProperty("oracle.jdbc.ReadTimeout", "30000"));
            config.addDataSourceProperty("oracle.net.READ_TIMEOUT", dbProperties.getProperty("oracle.net.READ_TIMEOUT", "30000"));

            // إعدادات الترميز
            // Encoding settings
            config.addDataSourceProperty("oracle.jdbc.defaultNChar", "true");
            config.addDataSourceProperty("oracle.jdbc.convertNcharLiterals", "true");
            
            // تعيين اسم مجموعة الاتصالات
            // Set pool name
            config.setPoolName("ShipmentERP-Pool");
            
            // إنشاء مجموعة الاتصالات
            // Create data source
            dataSource = new HikariDataSource(config);
            
            // اختبار الاتصال
            // Test connection
            testConnection();
            
            logger.info("تم تهيئة مجموعة اتصالات قاعدة البيانات بنجاح");
            
        } catch (ClassNotFoundException e) {
            logger.error("تعذر العثور على تعريف Oracle JDBC", e);
            throw new RuntimeException("Oracle JDBC driver not found", e);
        } catch (Exception e) {
            logger.error("فشل في تهيئة قاعدة البيانات", e);
            throw new RuntimeException("Failed to initialize database", e);
        }
    }
    
    /**
     * اختبار الاتصال بقاعدة البيانات
     * Test database connection
     */
    private void testConnection() throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(5)) {
                logger.info("تم اختبار الاتصال بقاعدة البيانات بنجاح");
            } else {
                throw new SQLException("Invalid database connection");
            }
        }
    }
    
    /**
     * الحصول على مصدر البيانات
     * Get data source
     */
    public DataSource getDataSource() {
        if (dataSource == null) {
            throw new IllegalStateException("Database not initialized. Call initialize() first.");
        }
        return dataSource;
    }
    
    /**
     * الحصول على اتصال بقاعدة البيانات
     * Get database connection
     */
    public Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new IllegalStateException("Database not initialized. Call initialize() first.");
        }
        return dataSource.getConnection();
    }
    
    /**
     * إغلاق مجموعة الاتصالات
     * Close data source
     */
    public void closeDataSource() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            logger.info("تم إغلاق مجموعة اتصالات قاعدة البيانات");
        }
    }
    
    /**
     * الحصول على خاصية من إعدادات قاعدة البيانات
     * Get property from database configuration
     */
    public String getProperty(String key) {
        return dbProperties.getProperty(key);
    }
    
    /**
     * الحصول على خاصية مع قيمة افتراضية
     * Get property with default value
     */
    public String getProperty(String key, String defaultValue) {
        return dbProperties.getProperty(key, defaultValue);
    }
    
    /**
     * التحقق من حالة الاتصال
     * Check connection status
     */
    public boolean isConnectionValid() {
        if (dataSource == null) {
            return false;
        }
        
        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5);
        } catch (SQLException e) {
            logger.warn("فشل في التحقق من صحة الاتصال", e);
            return false;
        }
    }
    
    /**
     * الحصول على معلومات مجموعة الاتصالات
     * Get connection pool information
     */
    public String getPoolInfo() {
        if (dataSource == null) {
            return "Database not initialized";
        }
        
        return String.format(
            "Pool Info - Active: %d, Idle: %d, Total: %d, Waiting: %d",
            dataSource.getHikariPoolMXBean().getActiveConnections(),
            dataSource.getHikariPoolMXBean().getIdleConnections(),
            dataSource.getHikariPoolMXBean().getTotalConnections(),
            dataSource.getHikariPoolMXBean().getThreadsAwaitingConnection()
        );
    }
}
