2025-07-17 19:36:42 [main] INFO  c.shipment.erp.ShipmentManagementApp - تشغيل نظام إدارة الشحنات - Launching Shipment Management System
2025-07-17 19:36:42 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v1.0.0
2025-07-17 19:36:42 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully
2025-07-17 19:36:42 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات قاعدة البيانات بنجاح
2025-07-17 19:36:43 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 19:36:44 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:134)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 19:51:10 [main] INFO  c.shipment.erp.ShipmentManagementApp - تشغيل نظام إدارة الشحنات - Launching Shipment Management System
2025-07-17 19:51:11 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v1.0.0
2025-07-17 19:51:11 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully
2025-07-17 19:51:11 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات قاعدة البيانات بنجاح
2025-07-17 19:51:11 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 19:51:12 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:134)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 19:52:42 [main] INFO  c.shipment.erp.ShipmentManagementApp - تشغيل نظام إدارة الشحنات - Launching Shipment Management System
2025-07-17 19:52:42 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v1.0.0
2025-07-17 19:52:42 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully
2025-07-17 19:52:42 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات قاعدة البيانات بنجاح
2025-07-17 19:52:43 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 19:52:43 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:135)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 19:55:18 [main] INFO  c.shipment.erp.ShipmentManagementApp - تشغيل نظام إدارة الشحنات - Launching Shipment Management System
2025-07-17 19:55:18 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v1.0.0
2025-07-17 19:55:18 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully
2025-07-17 19:55:18 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات قاعدة البيانات بنجاح
2025-07-17 19:55:19 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 19:55:19 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:130)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 20:18:51 [main] INFO  c.shipment.erp.ShipmentManagementApp - تشغيل نظام إدارة الشحنات - Launching Shipment Management System
2025-07-17 20:18:52 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v1.0.0
2025-07-17 20:18:52 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully
2025-07-17 20:18:52 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات قاعدة البيانات بنجاح
2025-07-17 20:18:53 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 20:18:54 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:130)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 20:21:31 [main] INFO  c.shipment.erp.ShipmentManagementApp - تشغيل نظام إدارة الشحنات - Launching Shipment Management System
2025-07-17 20:21:31 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v1.0.0
2025-07-17 20:21:31 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully
2025-07-17 20:21:31 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات قاعدة البيانات بنجاح
2025-07-17 20:21:32 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 20:21:33 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:135)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 20:24:21 [main] INFO  c.shipment.erp.ShipmentManagementApp - تشغيل نظام إدارة الشحنات - Launching Shipment Management System
2025-07-17 20:24:21 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v1.0.0
2025-07-17 20:24:21 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully
2025-07-17 20:24:21 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات قاعدة البيانات بنجاح
2025-07-17 20:24:22 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات Oracle بنجاح - Oracle properties loaded successfully
2025-07-17 20:24:22 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 20:24:24 [JavaFX-Launcher] INFO  com.zaxxer.hikari.pool.HikariPool - ShipmentERP-Pool - Added connection oracle.jdbc.driver.T4CConnection@37b584a4
2025-07-17 20:24:24 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Start completed.
2025-07-17 20:24:24 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم اختبار الاتصال بقاعدة البيانات بنجاح
2025-07-17 20:24:24 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تهيئة مجموعة اتصالات قاعدة البيانات بنجاح
2025-07-17 20:24:24 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم الاتصال بقاعدة البيانات بنجاح - Database connection established successfully
2025-07-17 20:24:24 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم تهيئة التطبيق بنجاح - Application initialized successfully
2025-07-17 20:24:24 [JavaFX Application Thread] WARN  c.shipment.erp.ShipmentManagementApp - تعذر تحميل أيقونة التطبيق - Could not load application icon
java.lang.NullPointerException: Input stream must not be null
	at javafx.graphics@21.0.1/javafx.scene.image.Image.validateInputStream(Image.java:1140)
	at javafx.graphics@21.0.1/javafx.scene.image.Image.<init>(Image.java:707)
	at com.shipment.erp.ShipmentManagementApp.setupPrimaryStage(ShipmentManagementApp.java:167)
	at com.shipment.erp.ShipmentManagementApp.start(ShipmentManagementApp.java:78)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.1/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.1/com.sun.glass.ui.win.WinApplication._runLoop(Native Method)
	at javafx.graphics@21.0.1/com.sun.glass.ui.win.WinApplication.lambda$runLoop$3(WinApplication.java:185)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-17 20:24:25 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تهيئة شاشة تسجيل الدخول - Initializing login screen
2025-07-17 20:24:25 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تم تهيئة خدمات المستخدم - User services initialized
2025-07-17 20:24:25 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تم تهيئة شاشة تسجيل الدخول بنجاح - Login screen initialized successfully
2025-07-17 20:24:27 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - تم عرض شاشة تسجيل الدخول - Login screen displayed
2025-07-17 20:24:36 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:24:36 [Thread-6] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:24:37 [Thread-6] WARN  c.shipment.erp.service.UserService - فشل التحقق من المستخدم: admin - Authentication failed for user: admin
2025-07-17 20:24:37 [Thread-6] INFO  c.shipment.erp.service.UserService - تم تحديث معلومات تسجيل الدخول الفاشل للمستخدم: admin - Updated failed login for user: admin
2025-07-17 20:24:37 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 1 - Failed login attempt: 1
2025-07-17 20:24:37 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في تسجيل الدخول - اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: 2
2025-07-17 20:24:57 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:24:57 [Thread-7] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:24:57 [Thread-7] WARN  c.shipment.erp.service.UserService - فشل التحقق من المستخدم: admin - Authentication failed for user: admin
2025-07-17 20:24:57 [Thread-7] INFO  c.shipment.erp.service.UserService - تم تحديث معلومات تسجيل الدخول الفاشل للمستخدم: admin - Updated failed login for user: admin
2025-07-17 20:24:57 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 2 - Failed login attempt: 2
2025-07-17 20:24:57 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في تسجيل الدخول - اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: 1
2025-07-17 20:25:44 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:25:45 [Thread-8] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:25:45 [Thread-8] WARN  c.shipment.erp.service.UserService - فشل التحقق من المستخدم: admin - Authentication failed for user: admin
2025-07-17 20:25:45 [Thread-8] INFO  c.shipment.erp.service.UserService - تم تحديث معلومات تسجيل الدخول الفاشل للمستخدم: admin - Updated failed login for user: admin
2025-07-17 20:25:45 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 3 - Failed login attempt: 3
2025-07-17 20:25:45 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: تم تجاوز الحد الأقصى - تم تجاوز الحد الأقصى للمحاولات (3). يرجى المحاولة لاحقاً.
2025-07-17 20:26:47 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:26:47 [Thread-10] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:26:47 [Thread-10] INFO  c.shipment.erp.service.UserService - المستخدم مقفل: admin - User is locked: admin
2025-07-17 20:26:47 [Thread-10] WARN  c.shipment.erp.service.UserService - الحساب مقفل: admin - Account locked: admin
2025-07-17 20:26:47 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 1 - Failed login attempt: 1
2025-07-17 20:26:47 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في تسجيل الدخول - اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: 2
2025-07-17 20:26:58 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - إلغاء تسجيل الدخول - Login cancelled
2025-07-17 20:27:03 [JavaFX Application Thread] INFO  com.shipment.erp.util.AlertUtil - عرض رسالة تأكيد: تأكيد الخروج - النتيجة: موافق
2025-07-17 20:27:03 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - إيقاف نظام إدارة الشحنات - Stopping Shipment Management System
2025-07-17 20:27:03 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Shutdown initiated...
2025-07-17 20:27:03 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Shutdown completed.
2025-07-17 20:27:03 [JavaFX Application Thread] INFO  c.shipment.erp.config.DatabaseConfig - تم إغلاق مجموعة اتصالات قاعدة البيانات
2025-07-17 20:27:03 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - تم إيقاف التطبيق بنجاح - Application stopped successfully
2025-07-17 20:28:52 [main] INFO  c.shipment.erp.ShipmentManagementApp - تشغيل نظام إدارة الشحنات - Launching Shipment Management System
2025-07-17 20:28:52 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v1.0.0
2025-07-17 20:28:52 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully
2025-07-17 20:28:52 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات قاعدة البيانات بنجاح
2025-07-17 20:28:52 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات Oracle بنجاح - Oracle properties loaded successfully
2025-07-17 20:28:52 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 20:28:54 [JavaFX-Launcher] INFO  com.zaxxer.hikari.pool.HikariPool - ShipmentERP-Pool - Added connection oracle.jdbc.driver.T4CConnection@4274b7a1
2025-07-17 20:28:54 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Start completed.
2025-07-17 20:28:54 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم اختبار الاتصال بقاعدة البيانات بنجاح
2025-07-17 20:28:54 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تهيئة مجموعة اتصالات قاعدة البيانات بنجاح
2025-07-17 20:28:54 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم الاتصال بقاعدة البيانات بنجاح - Database connection established successfully
2025-07-17 20:28:54 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم تهيئة التطبيق بنجاح - Application initialized successfully
2025-07-17 20:28:54 [JavaFX Application Thread] WARN  c.shipment.erp.ShipmentManagementApp - تعذر تحميل أيقونة التطبيق - Could not load application icon
java.lang.NullPointerException: Input stream must not be null
	at javafx.graphics@21.0.1/javafx.scene.image.Image.validateInputStream(Image.java:1140)
	at javafx.graphics@21.0.1/javafx.scene.image.Image.<init>(Image.java:707)
	at com.shipment.erp.ShipmentManagementApp.setupPrimaryStage(ShipmentManagementApp.java:167)
	at com.shipment.erp.ShipmentManagementApp.start(ShipmentManagementApp.java:78)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.1/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.1/com.sun.glass.ui.win.WinApplication._runLoop(Native Method)
	at javafx.graphics@21.0.1/com.sun.glass.ui.win.WinApplication.lambda$runLoop$3(WinApplication.java:185)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-17 20:28:55 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تهيئة شاشة تسجيل الدخول - Initializing login screen
2025-07-17 20:28:55 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تم تهيئة خدمات المستخدم - User services initialized
2025-07-17 20:28:55 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تم تهيئة شاشة تسجيل الدخول بنجاح - Login screen initialized successfully
2025-07-17 20:28:56 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - تم عرض شاشة تسجيل الدخول - Login screen displayed
2025-07-17 20:29:01 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:29:01 [Thread-6] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:29:01 [Thread-6] INFO  c.shipment.erp.service.UserService - المستخدم مقفل: admin - User is locked: admin
2025-07-17 20:29:01 [Thread-6] WARN  c.shipment.erp.service.UserService - الحساب مقفل: admin - Account locked: admin
2025-07-17 20:29:01 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 1 - Failed login attempt: 1
2025-07-17 20:29:01 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في تسجيل الدخول - اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: 2
2025-07-17 20:30:48 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - إلغاء تسجيل الدخول - Login cancelled
2025-07-17 20:30:50 [JavaFX Application Thread] INFO  com.shipment.erp.util.AlertUtil - عرض رسالة تأكيد: تأكيد الخروج - النتيجة: موافق
2025-07-17 20:30:50 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - إيقاف نظام إدارة الشحنات - Stopping Shipment Management System
2025-07-17 20:30:50 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Shutdown initiated...
2025-07-17 20:30:50 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Shutdown completed.
2025-07-17 20:30:50 [JavaFX Application Thread] INFO  c.shipment.erp.config.DatabaseConfig - تم إغلاق مجموعة اتصالات قاعدة البيانات
2025-07-17 20:30:50 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - تم إيقاف التطبيق بنجاح - Application stopped successfully
2025-07-17 20:42:24 [main] INFO  c.shipment.erp.ShipmentManagementApp - تشغيل نظام إدارة الشحنات - Launching Shipment Management System
2025-07-17 20:42:24 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v1.0.0
2025-07-17 20:42:24 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully
2025-07-17 20:42:24 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات قاعدة البيانات بنجاح
2025-07-17 20:42:24 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات Oracle بنجاح - Oracle properties loaded successfully
2025-07-17 20:42:24 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 20:42:26 [JavaFX-Launcher] INFO  com.zaxxer.hikari.pool.HikariPool - ShipmentERP-Pool - Added connection oracle.jdbc.driver.T4CConnection@97cf541
2025-07-17 20:42:26 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Start completed.
2025-07-17 20:42:26 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم اختبار الاتصال بقاعدة البيانات بنجاح
2025-07-17 20:42:26 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تهيئة مجموعة اتصالات قاعدة البيانات بنجاح
2025-07-17 20:42:26 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم الاتصال بقاعدة البيانات بنجاح - Database connection established successfully
2025-07-17 20:42:26 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم تهيئة التطبيق بنجاح - Application initialized successfully
2025-07-17 20:42:26 [JavaFX Application Thread] WARN  c.shipment.erp.ShipmentManagementApp - تعذر تحميل أيقونة التطبيق - Could not load application icon
java.lang.NullPointerException: Input stream must not be null
	at javafx.graphics@21.0.1/javafx.scene.image.Image.validateInputStream(Image.java:1140)
	at javafx.graphics@21.0.1/javafx.scene.image.Image.<init>(Image.java:707)
	at com.shipment.erp.ShipmentManagementApp.setupPrimaryStage(ShipmentManagementApp.java:167)
	at com.shipment.erp.ShipmentManagementApp.start(ShipmentManagementApp.java:78)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.1/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.1/com.sun.glass.ui.win.WinApplication._runLoop(Native Method)
	at javafx.graphics@21.0.1/com.sun.glass.ui.win.WinApplication.lambda$runLoop$3(WinApplication.java:185)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-17 20:42:27 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تهيئة شاشة تسجيل الدخول - Initializing login screen
2025-07-17 20:42:27 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تم تهيئة خدمات المستخدم - User services initialized
2025-07-17 20:42:27 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تم تهيئة شاشة تسجيل الدخول بنجاح - Login screen initialized successfully
2025-07-17 20:42:28 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - تم عرض شاشة تسجيل الدخول - Login screen displayed
2025-07-17 20:42:39 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:42:39 [Thread-6] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:42:40 [Thread-6] WARN  c.shipment.erp.service.UserService - فشل التحقق من المستخدم: admin - Authentication failed for user: admin
2025-07-17 20:42:40 [Thread-6] INFO  c.shipment.erp.service.UserService - تم تحديث معلومات تسجيل الدخول الفاشل للمستخدم: admin - Updated failed login for user: admin
2025-07-17 20:42:40 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 1 - Failed login attempt: 1
2025-07-17 20:42:40 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في تسجيل الدخول - اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: 2
2025-07-17 20:43:04 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:43:04 [Thread-7] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:43:04 [Thread-7] INFO  c.shipment.erp.service.UserService - المستخدم مقفل: admin - User is locked: admin
2025-07-17 20:43:04 [Thread-7] WARN  c.shipment.erp.service.UserService - الحساب مقفل: admin - Account locked: admin
2025-07-17 20:43:04 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 2 - Failed login attempt: 2
2025-07-17 20:43:04 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في تسجيل الدخول - اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: 1
2025-07-17 20:44:07 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:44:07 [Thread-8] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:44:07 [Thread-8] INFO  c.shipment.erp.service.UserService - المستخدم مقفل: admin - User is locked: admin
2025-07-17 20:44:07 [Thread-8] WARN  c.shipment.erp.service.UserService - الحساب مقفل: admin - Account locked: admin
2025-07-17 20:44:07 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 3 - Failed login attempt: 3
2025-07-17 20:44:07 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: تم تجاوز الحد الأقصى - تم تجاوز الحد الأقصى للمحاولات (3). يرجى المحاولة لاحقاً.
2025-07-17 20:45:10 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:45:10 [Thread-10] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:45:10 [Thread-10] INFO  c.shipment.erp.service.UserService - المستخدم مقفل: admin - User is locked: admin
2025-07-17 20:45:10 [Thread-10] WARN  c.shipment.erp.service.UserService - الحساب مقفل: admin - Account locked: admin
2025-07-17 20:45:10 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 1 - Failed login attempt: 1
2025-07-17 20:45:10 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في تسجيل الدخول - اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: 2
2025-07-17 20:45:57 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:45:57 [Thread-11] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:45:57 [Thread-11] INFO  c.shipment.erp.service.UserService - المستخدم مقفل: admin - User is locked: admin
2025-07-17 20:45:57 [Thread-11] WARN  c.shipment.erp.service.UserService - الحساب مقفل: admin - Account locked: admin
2025-07-17 20:45:57 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 2 - Failed login attempt: 2
2025-07-17 20:45:57 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في تسجيل الدخول - اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: 1
2025-07-17 20:46:18 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - طلب إغلاق التطبيق من المستخدم - User requested application close
2025-07-17 20:46:18 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - إيقاف نظام إدارة الشحنات - Stopping Shipment Management System
2025-07-17 20:46:18 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Shutdown initiated...
2025-07-17 20:46:18 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Shutdown completed.
2025-07-17 20:46:18 [JavaFX Application Thread] INFO  c.shipment.erp.config.DatabaseConfig - تم إغلاق مجموعة اتصالات قاعدة البيانات
2025-07-17 20:46:18 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - تم إيقاف التطبيق بنجاح - Application stopped successfully
2025-07-17 20:50:45 [main] INFO  c.shipment.erp.ShipmentManagementApp - تشغيل نظام إدارة الشحنات - Launching Shipment Management System
2025-07-17 20:50:45 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v1.0.0
2025-07-17 20:50:45 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully
2025-07-17 20:50:45 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات قاعدة البيانات بنجاح
2025-07-17 20:50:46 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات Oracle بنجاح - Oracle properties loaded successfully
2025-07-17 20:50:46 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 20:50:47 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:154)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 20:55:52 [main] INFO  c.shipment.erp.ShipmentManagementApp - تشغيل نظام إدارة الشحنات - Launching Shipment Management System
2025-07-17 20:55:52 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v1.0.0
2025-07-17 20:55:53 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully
2025-07-17 20:55:53 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات قاعدة البيانات بنجاح
2025-07-17 20:55:53 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات Oracle بنجاح - Oracle properties loaded successfully
2025-07-17 20:55:53 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 20:55:55 [JavaFX-Launcher] INFO  com.zaxxer.hikari.pool.HikariPool - ShipmentERP-Pool - Added connection oracle.jdbc.driver.T4CConnection@97cf541
2025-07-17 20:55:55 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Start completed.
2025-07-17 20:55:55 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم اختبار الاتصال بقاعدة البيانات بنجاح
2025-07-17 20:55:55 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تهيئة مجموعة اتصالات قاعدة البيانات بنجاح
2025-07-17 20:55:55 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم الاتصال بقاعدة البيانات بنجاح - Database connection established successfully
2025-07-17 20:55:55 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم تهيئة التطبيق بنجاح - Application initialized successfully
2025-07-17 20:55:55 [JavaFX Application Thread] WARN  c.shipment.erp.ShipmentManagementApp - تعذر تحميل أيقونة التطبيق - Could not load application icon
java.lang.NullPointerException: Input stream must not be null
	at javafx.graphics@21.0.1/javafx.scene.image.Image.validateInputStream(Image.java:1140)
	at javafx.graphics@21.0.1/javafx.scene.image.Image.<init>(Image.java:707)
	at com.shipment.erp.ShipmentManagementApp.setupPrimaryStage(ShipmentManagementApp.java:167)
	at com.shipment.erp.ShipmentManagementApp.start(ShipmentManagementApp.java:78)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.1/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.1/com.sun.glass.ui.win.WinApplication._runLoop(Native Method)
	at javafx.graphics@21.0.1/com.sun.glass.ui.win.WinApplication.lambda$runLoop$3(WinApplication.java:185)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-17 20:55:56 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تهيئة شاشة تسجيل الدخول - Initializing login screen
2025-07-17 20:55:56 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تم تهيئة خدمات المستخدم - User services initialized
2025-07-17 20:55:56 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تم تهيئة شاشة تسجيل الدخول بنجاح - Login screen initialized successfully
2025-07-17 20:55:59 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - تم عرض شاشة تسجيل الدخول - Login screen displayed
2025-07-17 20:56:05 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:56:06 [Thread-6] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:56:06 [Thread-6] INFO  c.shipment.erp.service.UserService - المستخدم مقفل: admin - User is locked: admin
2025-07-17 20:56:06 [Thread-6] WARN  c.shipment.erp.service.UserService - الحساب مقفل: admin - Account locked: admin
2025-07-17 20:56:06 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 1 - Failed login attempt: 1
2025-07-17 20:56:06 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في تسجيل الدخول - اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: 2
2025-07-17 20:56:23 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:56:23 [Thread-7] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:56:24 [Thread-7] INFO  c.shipment.erp.service.UserService - المستخدم مقفل: admin - User is locked: admin
2025-07-17 20:56:24 [Thread-7] WARN  c.shipment.erp.service.UserService - الحساب مقفل: admin - Account locked: admin
2025-07-17 20:56:24 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 2 - Failed login attempt: 2
2025-07-17 20:56:24 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في تسجيل الدخول - اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: 1
2025-07-17 20:58:54 [main] INFO  c.shipment.erp.ShipmentManagementApp - تشغيل نظام إدارة الشحنات - Launching Shipment Management System
2025-07-17 20:58:54 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v1.0.0
2025-07-17 20:58:55 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully
2025-07-17 20:58:55 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات قاعدة البيانات بنجاح
2025-07-17 20:58:55 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تحميل إعدادات Oracle بنجاح - Oracle properties loaded successfully
2025-07-17 20:58:55 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Starting...
2025-07-17 20:58:56 [JavaFX-Launcher] INFO  com.zaxxer.hikari.pool.HikariPool - ShipmentERP-Pool - Added connection oracle.jdbc.driver.T4CConnection@97cf541
2025-07-17 20:58:56 [JavaFX-Launcher] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Start completed.
2025-07-17 20:58:56 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم اختبار الاتصال بقاعدة البيانات بنجاح
2025-07-17 20:58:56 [JavaFX-Launcher] INFO  c.shipment.erp.config.DatabaseConfig - تم تهيئة مجموعة اتصالات قاعدة البيانات بنجاح
2025-07-17 20:58:56 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم الاتصال بقاعدة البيانات بنجاح - Database connection established successfully
2025-07-17 20:58:56 [JavaFX-Launcher] INFO  c.shipment.erp.ShipmentManagementApp - تم تهيئة التطبيق بنجاح - Application initialized successfully
2025-07-17 20:58:56 [JavaFX Application Thread] WARN  c.shipment.erp.ShipmentManagementApp - تعذر تحميل أيقونة التطبيق - Could not load application icon
java.lang.NullPointerException: Input stream must not be null
	at javafx.graphics@21.0.1/javafx.scene.image.Image.validateInputStream(Image.java:1140)
	at javafx.graphics@21.0.1/javafx.scene.image.Image.<init>(Image.java:707)
	at com.shipment.erp.ShipmentManagementApp.setupPrimaryStage(ShipmentManagementApp.java:167)
	at com.shipment.erp.ShipmentManagementApp.start(ShipmentManagementApp.java:78)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication1$9(LauncherImpl.java:839)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runAndWait$12(PlatformImpl.java:483)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runLater$10(PlatformImpl.java:456)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at javafx.graphics@21.0.1/com.sun.javafx.application.PlatformImpl.lambda$runLater$11(PlatformImpl.java:455)
	at javafx.graphics@21.0.1/com.sun.glass.ui.InvokeLaterDispatcher$Future.run(InvokeLaterDispatcher.java:95)
	at javafx.graphics@21.0.1/com.sun.glass.ui.win.WinApplication._runLoop(Native Method)
	at javafx.graphics@21.0.1/com.sun.glass.ui.win.WinApplication.lambda$runLoop$3(WinApplication.java:185)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-17 20:58:57 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تهيئة شاشة تسجيل الدخول - Initializing login screen
2025-07-17 20:58:57 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تم تهيئة خدمات المستخدم - User services initialized
2025-07-17 20:58:57 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - تم تهيئة شاشة تسجيل الدخول بنجاح - Login screen initialized successfully
2025-07-17 20:58:59 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - تم عرض شاشة تسجيل الدخول - Login screen displayed
2025-07-17 20:59:06 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:59:06 [Thread-6] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:59:07 [Thread-6] WARN  c.shipment.erp.service.UserService - فشل التحقق من المستخدم: admin - Authentication failed for user: admin
2025-07-17 20:59:07 [Thread-6] INFO  c.shipment.erp.service.UserService - تم تحديث معلومات تسجيل الدخول الفاشل للمستخدم: admin - Updated failed login for user: admin
2025-07-17 20:59:07 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 1 - Failed login attempt: 1
2025-07-17 20:59:07 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في تسجيل الدخول - اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: 2
2025-07-17 20:59:27 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 20:59:27 [Thread-7] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 20:59:27 [Thread-7] INFO  c.shipment.erp.service.UserService - المستخدم مقفل: admin - User is locked: admin
2025-07-17 20:59:27 [Thread-7] WARN  c.shipment.erp.service.UserService - الحساب مقفل: admin - Account locked: admin
2025-07-17 20:59:27 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 2 - Failed login attempt: 2
2025-07-17 20:59:27 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في تسجيل الدخول - اسم المستخدم أو كلمة المرور غير صحيحة. المحاولات المتبقية: 1
2025-07-17 21:00:24 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 21:00:24 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في البيانات - كلمة المرور يجب أن تكون 6 أحرف على الأقل
2025-07-17 21:00:24 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 21:00:24 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: خطأ في البيانات - كلمة المرور يجب أن تكون 6 أحرف على الأقل
2025-07-17 21:01:27 [JavaFX Application Thread] INFO  c.s.erp.controller.LoginController - محاولة تسجيل دخول - Login attempt
2025-07-17 21:01:27 [Thread-8] INFO  c.shipment.erp.service.UserService - محاولة التحقق من المستخدم: admin - Authenticating user: admin
2025-07-17 21:01:27 [Thread-8] INFO  c.shipment.erp.service.UserService - المستخدم مقفل: admin - User is locked: admin
2025-07-17 21:01:27 [Thread-8] WARN  c.shipment.erp.service.UserService - الحساب مقفل: admin - Account locked: admin
2025-07-17 21:01:27 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - محاولة تسجيل دخول فاشلة رقم: 3 - Failed login attempt: 3
2025-07-17 21:01:27 [JavaFX Application Thread] WARN  c.s.erp.controller.LoginController - رسالة خطأ: تم تجاوز الحد الأقصى - تم تجاوز الحد الأقصى للمحاولات (3). يرجى المحاولة لاحقاً.
2025-07-17 21:02:15 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - طلب إغلاق التطبيق من المستخدم - User requested application close
2025-07-17 21:02:16 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - إيقاف نظام إدارة الشحنات - Stopping Shipment Management System
2025-07-17 21:02:16 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Shutdown initiated...
2025-07-17 21:02:16 [JavaFX Application Thread] INFO  com.zaxxer.hikari.HikariDataSource - ShipmentERP-Pool - Shutdown completed.
2025-07-17 21:02:16 [JavaFX Application Thread] INFO  c.shipment.erp.config.DatabaseConfig - تم إغلاق مجموعة اتصالات قاعدة البيانات
2025-07-17 21:02:16 [JavaFX Application Thread] INFO  c.shipment.erp.ShipmentManagementApp - تم إيقاف التطبيق بنجاح - Application stopped successfully
