/* 
 * الأنماط الرئيسية لنظام إدارة الشحنات
 * Main Styles for Shipment Management System
 * 
 * يدعم اللغة العربية مع محاذاة RTL
 * Supports Arabic language with RTL alignment
 */

/* ===== الخطوط والنصوص العامة ===== */
/* ===== General Fonts and Text ===== */
.root {
    -fx-font-family: "Tahoma", "Arial Unicode MS", "Segoe UI", sans-serif;
    -fx-font-size: 12px;
    -fx-base: #f4f4f4;
    -fx-background: #ffffff;
    -fx-control-inner-background: #ffffff;
    -fx-control-inner-background-alt: #f8f8f8;
    -fx-accent: #0078d4;
    -fx-default-button: #0078d4;
    -fx-focus-color: #0078d4;
    -fx-faint-focus-color: #0078d422;
    -fx-selection-bar: #0078d4;
    -fx-selection-bar-non-focused: #cccccc;
}

/* ===== النصوص العربية ===== */
/* ===== Arabic Text ===== */
.arabic-text {
    -fx-font-family: "Tahoma", "Arial Unicode MS", "Traditional Arabic", sans-serif;
    -fx-font-size: 13px;
    -fx-text-alignment: right;
}

.arabic-title {
    -fx-font-family: "Tahoma", "Arial Unicode MS", "Traditional Arabic", sans-serif;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-alignment: right;
    -fx-text-fill: #2c3e50;
}

.arabic-header {
    -fx-font-family: "Tahoma", "Arial Unicode MS", "Traditional Arabic", sans-serif;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-alignment: right;
    -fx-text-fill: #34495e;
}

.arabic-label {
    -fx-font-family: "Tahoma", "Arial Unicode MS", "Traditional Arabic", sans-serif;
    -fx-font-size: 12px;
    -fx-text-alignment: right;
    -fx-text-fill: #2c3e50;
}

/* ===== الأزرار ===== */
/* ===== Buttons ===== */
.button {
    -fx-background-color: linear-gradient(to bottom, #ffffff, #f0f0f0);
    -fx-background-radius: 4px;
    -fx-border-color: #cccccc;
    -fx-border-radius: 4px;
    -fx-border-width: 1px;
    -fx-padding: 8px 16px;
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 12px;
    -fx-text-fill: #333333;
    -fx-cursor: hand;
}

.button:hover {
    -fx-background-color: linear-gradient(to bottom, #f8f8f8, #e8e8e8);
    -fx-border-color: #999999;
}

.button:pressed {
    -fx-background-color: linear-gradient(to bottom, #e8e8e8, #d8d8d8);
    -fx-border-color: #666666;
}

.button:focused {
    -fx-border-color: #0078d4;
    -fx-border-width: 2px;
}

/* أزرار أساسية */
/* Primary buttons */
.button-primary {
    -fx-background-color: linear-gradient(to bottom, #0078d4, #106ebe);
    -fx-text-fill: white;
    -fx-border-color: #106ebe;
}

.button-primary:hover {
    -fx-background-color: linear-gradient(to bottom, #106ebe, #005a9e);
}

.button-primary:pressed {
    -fx-background-color: linear-gradient(to bottom, #005a9e, #004578);
}

/* أزرار النجاح */
/* Success buttons */
.button-success {
    -fx-background-color: linear-gradient(to bottom, #28a745, #218838);
    -fx-text-fill: white;
    -fx-border-color: #218838;
}

.button-success:hover {
    -fx-background-color: linear-gradient(to bottom, #218838, #1e7e34);
}

/* أزرار التحذير */
/* Warning buttons */
.button-warning {
    -fx-background-color: linear-gradient(to bottom, #ffc107, #e0a800);
    -fx-text-fill: #212529;
    -fx-border-color: #e0a800;
}

.button-warning:hover {
    -fx-background-color: linear-gradient(to bottom, #e0a800, #d39e00);
}

/* أزرار الخطر */
/* Danger buttons */
.button-danger {
    -fx-background-color: linear-gradient(to bottom, #dc3545, #c82333);
    -fx-text-fill: white;
    -fx-border-color: #c82333;
}

.button-danger:hover {
    -fx-background-color: linear-gradient(to bottom, #c82333, #bd2130);
}

/* ===== حقول الإدخال ===== */
/* ===== Input Fields ===== */
.text-field, .text-area, .password-field {
    -fx-background-color: white;
    -fx-border-color: #cccccc;
    -fx-border-radius: 4px;
    -fx-border-width: 1px;
    -fx-padding: 8px 12px;
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 12px;
    -fx-text-fill: #333333;
}

.text-field:focused, .text-area:focused, .password-field:focused {
    -fx-border-color: #0078d4;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, #0078d422, 4, 0, 0, 0);
}

.text-field:error, .text-area:error, .password-field:error {
    -fx-border-color: #dc3545;
    -fx-border-width: 2px;
}

/* ===== القوائم المنسدلة ===== */
/* ===== ComboBoxes ===== */
.combo-box {
    -fx-background-color: white;
    -fx-border-color: #cccccc;
    -fx-border-radius: 4px;
    -fx-border-width: 1px;
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 12px;
}

.combo-box:focused {
    -fx-border-color: #0078d4;
    -fx-border-width: 2px;
}

.combo-box .list-cell {
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

/* ===== الجداول ===== */
/* ===== Tables ===== */
.table-view {
    -fx-background-color: white;
    -fx-border-color: #cccccc;
    -fx-border-width: 1px;
    -fx-table-cell-border-color: #e0e0e0;
}

.table-view .column-header {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 1px 1px 0;
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.table-view .column-header-background {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
}

.table-row-cell {
    -fx-background-color: white;
    -fx-border-color: transparent;
    -fx-table-cell-border-color: #e0e0e0;
}

.table-row-cell:odd {
    -fx-background-color: #f8f9fa;
}

.table-row-cell:selected {
    -fx-background-color: #0078d4;
    -fx-text-fill: white;
}

.table-row-cell:hover {
    -fx-background-color: #e3f2fd;
}

.table-cell {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 11px;
    -fx-padding: 4px 8px;
}

/* ===== شريط القوائم ===== */
/* ===== Menu Bar ===== */
.menu-bar {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1px 0;
}

.menu {
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 12px;
}

.menu-item {
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 12px;
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

.context-menu {
    -fx-background-color: white;
    -fx-border-color: #cccccc;
    -fx-border-width: 1px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 6, 0, 0, 2);
}

/* ===== شريط الأدوات ===== */
/* ===== Tool Bar ===== */
.tool-bar {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1px 0;
    -fx-padding: 8px;
    -fx-spacing: 8px;
}

/* ===== شريط الحالة ===== */
/* ===== Status Bar ===== */
.status-bar {
    -fx-background-color: linear-gradient(to bottom, #e9ecef, #f8f9fa);
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px 0 0 0;
    -fx-padding: 4px 8px;
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 11px;
    -fx-text-fill: #6c757d;
}

/* ===== التبويبات ===== */
/* ===== Tabs ===== */
.tab-pane {
    -fx-background-color: white;
    -fx-border-color: #cccccc;
    -fx-border-width: 1px;
}

.tab {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px 1px 0 1px;
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 12px;
    -fx-text-fill: #495057;
    -fx-padding: 8px 16px;
}

.tab:selected {
    -fx-background-color: white;
    -fx-text-fill: #0078d4;
    -fx-font-weight: bold;
}

.tab:hover {
    -fx-background-color: linear-gradient(to bottom, #e9ecef, #dee2e6);
}

/* ===== الألواح ===== */
/* ===== Panels ===== */
.titled-pane {
    -fx-background-color: white;
    -fx-border-color: #cccccc;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
}

.titled-pane > .title {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 13px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-padding: 8px 12px;
}

.titled-pane > .content {
    -fx-background-color: white;
    -fx-padding: 12px;
}

/* ===== شريط التقدم ===== */
/* ===== Progress Bar ===== */
.progress-bar {
    -fx-background-color: #e9ecef;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
}

.progress-bar > .bar {
    -fx-background-color: linear-gradient(to right, #0078d4, #106ebe);
    -fx-background-radius: 3px;
    -fx-padding: 4px;
}

/* ===== مؤشر التحميل ===== */
/* ===== Progress Indicator ===== */
.progress-indicator {
    -fx-progress-color: #0078d4;
}

/* ===== التمرير ===== */
/* ===== Scroll Bars ===== */
.scroll-bar {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
}

.scroll-bar .thumb {
    -fx-background-color: #6c757d;
    -fx-background-radius: 4px;
}

.scroll-bar .thumb:hover {
    -fx-background-color: #495057;
}

.scroll-bar .thumb:pressed {
    -fx-background-color: #343a40;
}

/* ===== الفواصل ===== */
/* ===== Separators ===== */
.separator {
    -fx-background-color: #dee2e6;
}

/* ===== التلميحات ===== */
/* ===== Tooltips ===== */
.tooltip {
    -fx-background-color: #343a40;
    -fx-text-fill: white;
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 11px;
    -fx-padding: 4px 8px;
    -fx-background-radius: 4px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 4, 0, 0, 1);
}

/* ===== الرسائل والتنبيهات ===== */
/* ===== Messages and Alerts ===== */
.alert {
    -fx-background-color: white;
    -fx-border-color: #cccccc;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 2);
}

.alert .header-panel {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

.alert .content {
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 12px;
    -fx-padding: 16px;
}

.success-alert {
    -fx-border-color: #28a745;
    -fx-border-width: 2px;
}

.success-alert .header-panel {
    -fx-background-color: linear-gradient(to bottom, #d4edda, #c3e6cb);
    -fx-text-fill: #155724;
}

.error-alert {
    -fx-border-color: #dc3545;
    -fx-border-width: 2px;
}

.error-alert .header-panel {
    -fx-background-color: linear-gradient(to bottom, #f8d7da, #f5c6cb);
    -fx-text-fill: #721c24;
}

.warning-alert {
    -fx-border-color: #ffc107;
    -fx-border-width: 2px;
}

.warning-alert .header-panel {
    -fx-background-color: linear-gradient(to bottom, #fff3cd, #ffeaa7);
    -fx-text-fill: #856404;
}
