package com.shipment.erp;

import java.io.IOException;
import java.util.Locale;
import java.util.ResourceBundle;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.shipment.erp.config.DatabaseConfig;
import com.shipment.erp.util.AlertUtil;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.image.Image;
import javafx.stage.Stage;

/**
 * التطبيق الرئيسي لنظام إدارة الشحنات
 * Main Application class for Shipment Management System
 * 
 * يدعم اللغة العربية مع محاذاة RTL
 * Supports Arabic language with RTL alignment
 * 
 * <AUTHOR> إدارة الشحنات
 * @version 1.0.0
 */
public class ShipmentManagementApp extends Application {
    
    private static final Logger logger = LoggerFactory.getLogger(ShipmentManagementApp.class);
    
    // إعدادات التطبيق الأساسية
    // Basic application settings
    private static final String APP_TITLE = "نظام إدارة الشحنات - Shipment Management System";
    private static final String APP_VERSION = "1.0.0";
    private static final int MIN_WIDTH = 1200;
    private static final int MIN_HEIGHT = 800;

    // الوضع التجريبي
    // Demo mode
    private static boolean demoMode = false;
    
    // مسارات الموارد
    // Resource paths
    private static final String MAIN_FXML = "/fxml/main.fxml";
    private static final String LOGIN_FXML = "/fxml/login.fxml";
    private static final String APP_ICON = "/images/app-icon.png";
    private static final String CSS_MAIN = "/css/main.css";
    private static final String CSS_RTL = "/css/rtl.css";
    
    private Stage primaryStage;
    private ResourceBundle resourceBundle;
    
    @Override
    public void init() throws Exception {
        super.init();
        
        logger.info("بدء تشغيل نظام إدارة الشحنات - Starting Shipment Management System v{}", APP_VERSION);
        
        // إعداد اللغة والمنطقة
        // Setup locale and language
        setupLocale();
        
        // تهيئة قاعدة البيانات
        // Initialize database
        initializeDatabase();
        
        logger.info("تم تهيئة التطبيق بنجاح - Application initialized successfully");
    }
    
    @Override
    public void start(Stage primaryStage) {
        this.primaryStage = primaryStage;
        
        try {
            // إعداد النافذة الرئيسية
            // Setup main window
            setupPrimaryStage();
            
            // عرض شاشة تسجيل الدخول
            // Show login screen
            showLoginScreen();
            
        } catch (Exception e) {
            logger.error("خطأ في بدء التطبيق - Error starting application", e);
            showErrorAndExit("خطأ في بدء التطبيق", "حدث خطأ غير متوقع أثناء بدء التطبيق. يرجى المحاولة مرة أخرى.");
        }
    }
    
    @Override
    public void stop() throws Exception {
        logger.info("إيقاف نظام إدارة الشحنات - Stopping Shipment Management System");
        
        try {
            // إغلاق اتصالات قاعدة البيانات
            // Close database connections
            DatabaseConfig.getInstance().closeDataSource();
            
        } catch (Exception e) {
            logger.error("خطأ أثناء إيقاف التطبيق - Error during application shutdown", e);
        }
        
        super.stop();
        logger.info("تم إيقاف التطبيق بنجاح - Application stopped successfully");
    }
    
    /**
     * إعداد اللغة والمنطقة للتطبيق
     * Setup application locale and language
     */
    private void setupLocale() {
        try {
            // تعيين اللغة العربية كافتراضية
            // Set Arabic as default locale
            Locale arabicLocale = new Locale("ar", "EG");
            Locale.setDefault(arabicLocale);
            
            // تحميل ملف الترجمة
            // Load resource bundle
            resourceBundle = ResourceBundle.getBundle("messages", arabicLocale);
            
            // إعداد خصائص النظام للعربية
            // Setup system properties for Arabic
            System.setProperty("user.language", "ar");
            System.setProperty("user.country", "EG");
            System.setProperty("file.encoding", "UTF-8");
            
            logger.info("تم إعداد اللغة العربية بنجاح - Arabic locale setup successfully");
            
        } catch (Exception e) {
            logger.warn("تعذر تحميل ملف الترجمة، سيتم استخدام الإنجليزية - Could not load Arabic resources, falling back to English", e);
            resourceBundle = ResourceBundle.getBundle("messages", Locale.ENGLISH);
        }
    }
    
    /**
     * تهيئة قاعدة البيانات
     * Initialize database connection
     */
    private void initializeDatabase() {
        if (demoMode) {
            logger.info("تشغيل النظام في الوضع التجريبي بدون قاعدة بيانات - Running in demo mode without database");
            return;
        }

        try {
            DatabaseConfig.getInstance().initialize();
            logger.info("تم الاتصال بقاعدة البيانات بنجاح - Database connection established successfully");

        } catch (Exception e) {
            logger.warn("فشل في الاتصال بقاعدة البيانات، التبديل للوضع التجريبي - Failed to connect to database, switching to demo mode", e);
            demoMode = true;
        }
    }
    
    /**
     * إعداد النافذة الرئيسية
     * Setup primary stage
     */
    private void setupPrimaryStage() {
        primaryStage.setTitle(APP_TITLE + " v" + APP_VERSION);
        primaryStage.setMinWidth(MIN_WIDTH);
        primaryStage.setMinHeight(MIN_HEIGHT);
        
        // تعيين أيقونة التطبيق
        // Set application icon
        try {
            Image icon = new Image(getClass().getResourceAsStream(APP_ICON));
            primaryStage.getIcons().add(icon);
        } catch (Exception e) {
            logger.warn("تعذر تحميل أيقونة التطبيق - Could not load application icon", e);
        }
        
        // إعداد إغلاق التطبيق
        // Setup application close behavior
        primaryStage.setOnCloseRequest(event -> {
            logger.info("طلب إغلاق التطبيق من المستخدم - User requested application close");
            Platform.exit();
        });
    }
    
    /**
     * عرض شاشة تسجيل الدخول
     * Show login screen
     */
    private void showLoginScreen() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource(LOGIN_FXML));
            loader.setResources(resourceBundle);
            
            Parent root = loader.load();
            
            Scene scene = new Scene(root);
            
            // تطبيق أنماط CSS
            // Apply CSS styles
            scene.getStylesheets().add(getClass().getResource(CSS_MAIN).toExternalForm());
            scene.getStylesheets().add(getClass().getResource(CSS_RTL).toExternalForm());
            
            primaryStage.setScene(scene);
            primaryStage.centerOnScreen();
            primaryStage.show();
            
            logger.info("تم عرض شاشة تسجيل الدخول - Login screen displayed");
            
        } catch (IOException e) {
            logger.error("فشل في تحميل شاشة تسجيل الدخول - Failed to load login screen", e);
            showErrorAndExit("خطأ في التطبيق", "تعذر تحميل واجهة تسجيل الدخول.");
        }
    }
    
    /**
     * عرض الشاشة الرئيسية بعد تسجيل الدخول بنجاح
     * Show main screen after successful login
     */
    public void showMainScreen() {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource(MAIN_FXML));
            loader.setResources(resourceBundle);
            
            Parent root = loader.load();
            
            Scene scene = new Scene(root);
            
            // تطبيق أنماط CSS
            // Apply CSS styles
            scene.getStylesheets().add(getClass().getResource(CSS_MAIN).toExternalForm());
            scene.getStylesheets().add(getClass().getResource(CSS_RTL).toExternalForm());
            
            primaryStage.setScene(scene);
            primaryStage.setMaximized(true);
            
            logger.info("تم عرض الشاشة الرئيسية - Main screen displayed");
            
        } catch (IOException e) {
            logger.error("فشل في تحميل الشاشة الرئيسية - Failed to load main screen", e);
            AlertUtil.showError("خطأ في التطبيق", "تعذر تحميل الشاشة الرئيسية.");
        }
    }
    
    /**
     * عرض رسالة خطأ وإنهاء التطبيق
     * Show error message and exit application
     */
    private void showErrorAndExit(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
            Platform.exit();
        });
    }
    
    /**
     * الحصول على النافذة الرئيسية
     * Get primary stage
     */
    public Stage getPrimaryStage() {
        return primaryStage;
    }
    
    /**
     * الحصول على ملف الترجمة
     * Get resource bundle
     */
    public ResourceBundle getResourceBundle() {
        return resourceBundle;
    }
    
    /**
     * نقطة دخول التطبيق
     * Application entry point
     */
    public static void main(String[] args) {
        // إعداد خصائص JavaFX للعربية
        // Setup JavaFX properties for Arabic
        System.setProperty("javafx.preloader", "com.shipment.erp.preloader.SplashScreenPreloader");
        System.setProperty("prism.lcdtext", "false");
        System.setProperty("prism.text", "t2k");
        
        logger.info("تشغيل نظام إدارة الشحنات - Launching Shipment Management System");
        
        try {
            launch(args);
        } catch (Exception e) {
            logger.error("خطأ فادح في التطبيق - Fatal application error", e);
            System.exit(1);
        }
    }
}
