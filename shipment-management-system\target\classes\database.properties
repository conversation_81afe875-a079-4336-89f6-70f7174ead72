# إعدادات قاعدة البيانات Oracle
# Oracle Database Configuration for Shipment Management System

# معلومات الاتصال الأساسية
# Basic Connection Information
db.url=***********************************
db.username=ship_erp
db.password=ys123
db.driver=oracle.jdbc.OracleDriver

# إعدادات الترميز العربي
# Arabic Encoding Settings
db.charset=AR8MSWIN1256
db.nls_lang=ARABIC_EGYPT.AR8MSWIN1256

# إعدادات Connection Pool
# Connection Pool Settings
db.pool.minimumIdle=5
db.pool.maximumPoolSize=20
db.pool.connectionTimeout=30000
db.pool.idleTimeout=600000
db.pool.maxLifetime=1800000
db.pool.leakDetectionThreshold=60000

# إعدادات الأداء
# Performance Settings
db.pool.cachePrepStmts=true
db.pool.prepStmtCacheSize=250
db.pool.prepStmtCacheSqlLimit=2048
db.pool.useServerPrepStmts=true

# إعدادات Oracle محددة
# Oracle Specific Settings
oracle.net.CONNECT_TIMEOUT=10000
oracle.jdbc.ReadTimeout=30000
oracle.net.READ_TIMEOUT=30000

# إعدادات SSL (اختيارية)
# SSL Settings (Optional)
db.ssl.enabled=false
db.ssl.truststore.path=
db.ssl.truststore.password=

# إعدادات التطبيق
# Application Settings
app.name=نظام إدارة الشحنات
app.version=1.0.0
app.locale=ar_EG
app.timezone=Africa/Cairo

# إعدادات السجلات
# Logging Settings
logging.level.root=INFO
logging.level.com.shipment.erp=DEBUG
logging.level.oracle.jdbc=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=logs/shipment-management.log
logging.file.max-size=10MB
logging.file.max-history=30

# إعدادات الأمان
# Security Settings
security.password.min.length=8
security.session.timeout=3600
security.max.login.attempts=3
security.lockout.duration=900

# إعدادات التقارير
# Reports Settings
reports.output.directory=reports
reports.temp.directory=temp
reports.default.format=PDF
reports.logo.path=images/logo.png

# إعدادات النسخ الاحتياطي
# Backup Settings
backup.enabled=true
backup.directory=backup
backup.schedule.daily=02:00
backup.retention.days=30
backup.compression=true

# إعدادات الإشعارات
# Notification Settings
notifications.enabled=true
notifications.email.smtp.host=
notifications.email.smtp.port=587
notifications.email.username=
notifications.email.password=
notifications.email.from=<EMAIL>

# إعدادات واجهة المستخدم
# UI Settings
ui.theme=default
ui.language=ar
ui.direction=rtl
ui.font.family=Tahoma
ui.font.size=12
ui.window.width=1200
ui.window.height=800
ui.window.maximized=false

# إعدادات التصدير والاستيراد
# Import/Export Settings
export.default.format=EXCEL
export.max.records=10000
import.batch.size=1000
import.validation.strict=true

# إعدادات الطباعة
# Printing Settings
print.default.printer=
print.page.size=A4
print.orientation=PORTRAIT
print.margins.top=20
print.margins.bottom=20
print.margins.left=20
print.margins.right=20
