<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<!--
الشاشة الرئيسية لنظام إدارة الشحنات
Main Screen for Shipment Management System

تدعم اللغة العربية مع محاذاة RTL
Supports Arabic language with RTL alignment
-->

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="com.shipment.erp.controller.MainController"
            styleClass="main-container">

   <!-- شريط القوائم الرئيسي -->
   <!-- Main Menu Bar -->
   <top>
      <VBox>
         <!-- شريط القوائم -->
         <!-- Menu Bar -->
         <MenuBar styleClass="main-menu-bar">

            <!-- قائمة ملف -->
            <!-- File Menu -->
            <Menu text="ملف">
               <MenuItem text="جديد" onAction="#handleNewFile" accelerator="Ctrl+N" />
               <MenuItem text="فتح" onAction="#handleOpenFile" accelerator="Ctrl+O" />
               <SeparatorMenuItem />
               <MenuItem text="حفظ" onAction="#handleSaveFile" accelerator="Ctrl+S" />
               <MenuItem text="حفظ باسم" onAction="#handleSaveAsFile" accelerator="Ctrl+Shift+S" />
               <SeparatorMenuItem />
               <MenuItem text="طباعة" onAction="#handlePrint" accelerator="Ctrl+P" />
               <MenuItem text="معاينة الطباعة" onAction="#handlePrintPreview" />
               <SeparatorMenuItem />
               <MenuItem text="تصدير" onAction="#handleExport" />
               <MenuItem text="استيراد" onAction="#handleImport" />
               <SeparatorMenuItem />
               <MenuItem text="خروج" onAction="#handleExit" accelerator="Alt+F4" />
            </Menu>

            <!-- قائمة تحرير -->
            <!-- Edit Menu -->
            <Menu text="تحرير">
               <MenuItem text="تراجع" onAction="#handleUndo" accelerator="Ctrl+Z" />
               <MenuItem text="إعادة" onAction="#handleRedo" accelerator="Ctrl+Y" />
               <SeparatorMenuItem />
               <MenuItem text="قص" onAction="#handleCut" accelerator="Ctrl+X" />
               <MenuItem text="نسخ" onAction="#handleCopy" accelerator="Ctrl+C" />
               <MenuItem text="لصق" onAction="#handlePaste" accelerator="Ctrl+V" />
               <MenuItem text="حذف" onAction="#handleDelete" accelerator="Delete" />
               <SeparatorMenuItem />
               <MenuItem text="تحديد الكل" onAction="#handleSelectAll" accelerator="Ctrl+A" />
               <MenuItem text="بحث" onAction="#handleFind" accelerator="Ctrl+F" />
               <MenuItem text="استبدال" onAction="#handleReplace" accelerator="Ctrl+H" />
               <SeparatorMenuItem />
               <MenuItem text="التفضيلات" onAction="#handlePreferences" />
            </Menu>

            <!-- قائمة عرض -->
            <!-- View Menu -->
            <Menu text="عرض">
               <CheckMenuItem fx:id="toolbarMenuItem" text="شريط الأدوات" selected="true"
                            onAction="#handleToggleToolbar" />
               <CheckMenuItem fx:id="statusbarMenuItem" text="شريط الحالة" selected="true"
                            onAction="#handleToggleStatusbar" />
               <CheckMenuItem fx:id="sidebarMenuItem" text="الشريط الجانبي" selected="true"
                            onAction="#handleToggleSidebar" />
               <SeparatorMenuItem />
               <MenuItem text="ملء الشاشة" onAction="#handleFullscreen" accelerator="F11" />
               <SeparatorMenuItem />
               <MenuItem text="تكبير" onAction="#handleZoomIn" accelerator="Ctrl+Plus" />
               <MenuItem text="تصغير" onAction="#handleZoomOut" accelerator="Ctrl+Minus" />
               <MenuItem text="إعادة تعيين التكبير" onAction="#handleZoomReset" accelerator="Ctrl+0" />
               <SeparatorMenuItem />
               <MenuItem text="تحديث" onAction="#handleRefresh" accelerator="F5" />
            </Menu>

            <!-- قائمة أدوات -->
            <!-- Tools Menu -->
            <Menu text="أدوات">
               <MenuItem text="آلة حاسبة" onAction="#handleCalculator" />
               <MenuItem text="تقويم" onAction="#handleCalendar" />
               <SeparatorMenuItem />
               <MenuItem text="التقارير" onAction="#handleReports" />
               <SeparatorMenuItem />
               <MenuItem text="نسخ احتياطي" onAction="#handleBackup" />
               <MenuItem text="استعادة" onAction="#handleRestore" />
               <SeparatorMenuItem />
               <MenuItem text="الإعدادات" onAction="#handleSettings" />
            </Menu>

            <!-- قائمة نافذة -->
            <!-- Window Menu -->
            <Menu text="نافذة">
               <MenuItem text="تبديل النوافذ" onAction="#handleSwitchWindows" accelerator="Ctrl+Tab" />
               <MenuItem text="إغلاق النافذة الحالية" onAction="#handleCloseCurrentWindow" accelerator="Ctrl+W" />
               <MenuItem text="إغلاق جميع النوافذ" onAction="#handleCloseAllWindows" />
               <SeparatorMenuItem />
               <MenuItem text="ترتيب النوافذ" onAction="#handleArrangeWindows" />
            </Menu>

            <!-- قائمة مساعدة -->
            <!-- Help Menu -->
            <Menu text="مساعدة">
               <MenuItem text="دليل المستخدم" onAction="#handleUserManual" accelerator="F1" />
               <MenuItem text="اختصارات لوحة المفاتيح" onAction="#handleShortcuts" />
               <SeparatorMenuItem />
               <MenuItem text="اتصل بنا" onAction="#handleContact" />
               <MenuItem text="التحديثات" onAction="#handleUpdates" />
               <SeparatorMenuItem />
               <MenuItem text="حول البرنامج" onAction="#handleAbout" />
            </Menu>
         </MenuBar>

         <!-- شريط الأدوات -->
         <!-- Tool Bar -->
         <ToolBar fx:id="mainToolBar" styleClass="main-toolbar">

            <!-- أزرار الملفات -->
            <!-- File Buttons -->
            <Button text="إضافة" onAction="#handleNewRecord" styleClass="toolbar-button">
               <tooltip>
                  <Tooltip text="إضافة سجل جديد" />
               </tooltip>
            </Button>

            <Button text="تعديل" onAction="#handleEditRecord" styleClass="toolbar-button">
               <tooltip>
                  <Tooltip text="تعديل السجل المحدد" />
               </tooltip>
            </Button>

            <Button text="حذف" onAction="#handleDeleteRecord" styleClass="toolbar-button">
               <tooltip>
                  <Tooltip text="حذف السجل المحدد" />
               </tooltip>
            </Button>

            <Separator />

            <!-- أزرار البحث والتصفية -->
            <!-- Search and Filter Buttons -->
            <Button text="بحث" onAction="#handleSearch" styleClass="toolbar-button">
               <tooltip>
                  <Tooltip text="البحث في السجلات" />
               </tooltip>
            </Button>

            <Button text="تصفية" onAction="#handleFilter" styleClass="toolbar-button">
               <tooltip>
                  <Tooltip text="تصفية السجلات" />
               </tooltip>
            </Button>

            <Button text="تحديث" onAction="#handleRefreshData" styleClass="toolbar-button">
               <tooltip>
                  <Tooltip text="تحديث البيانات" />
               </tooltip>
            </Button>

            <Separator />

            <!-- أزرار التقارير -->
            <!-- Report Buttons -->
            <Button text="طباعة" onAction="#handlePrintReport" styleClass="toolbar-button">
               <tooltip>
                  <Tooltip text="طباعة التقرير" />
               </tooltip>
            </Button>

            <Button text="تصدير" onAction="#handleExportData" styleClass="toolbar-button">
               <tooltip>
                  <Tooltip text="تصدير البيانات" />
               </tooltip>
            </Button>

            <!-- مساحة مرنة -->
            <!-- Flexible Space -->
            <Region HBox.hgrow="ALWAYS" />

            <!-- معلومات المستخدم -->
            <!-- User Information -->
            <Label fx:id="userLabel" text="المستخدم: admin" styleClass="user-label" />

            <Separator />

            <!-- زر تسجيل الخروج -->
            <!-- Logout Button -->
            <Button text="تسجيل الخروج" onAction="#handleLogout" styleClass="logout-button">
               <tooltip>
                  <Tooltip text="تسجيل الخروج من النظام" />
               </tooltip>
            </Button>
         </ToolBar>
      </VBox>
   </top>

   <!-- المحتوى الرئيسي -->
   <!-- Main Content -->
   <center>
      <SplitPane dividerPositions="0.25" styleClass="main-split-pane">

         <!-- الشريط الجانبي -->
         <!-- Sidebar -->
         <VBox fx:id="sidebar" styleClass="sidebar" prefWidth="250">
            <padding>
               <Insets bottom="10" left="10" right="10" top="10" />
            </padding>

            <!-- عنوان الشريط الجانبي -->
            <!-- Sidebar Title -->
            <Label text="القوائم الرئيسية" styleClass="sidebar-title">
               <font>
                  <Font name="Tahoma Bold" size="14" />
               </font>
               <VBox.margin>
                  <Insets bottom="15" />
               </VBox.margin>
            </Label>

            <!-- شجرة التنقل -->
            <!-- Navigation Tree -->
            <TreeView fx:id="navigationTree" styleClass="navigation-tree" VBox.vgrow="ALWAYS">
               <root>
                  <TreeItem value="نظام إدارة الشحنات" expanded="true">
                     <children>
                        <TreeItem value="الإعدادات العامة" expanded="true">
                           <children>
                              <TreeItem value="المتغيرات العامة للبرنامج" />
                              <TreeItem value="إعداد السنة المالية" />
                              <TreeItem value="تهيئة العملات" />
                              <TreeItem value="بيانات الشركة" />
                              <TreeItem value="بيانات الفروع" />
                              <TreeItem value="بيانات المستخدمين" />
                              <TreeItem value="صلاحيات المستخدمين" />
                              <TreeItem value="فتح سنة جديدة" />
                              <TreeItem value="النسخ الاحتياطي والاستعادة" />
                           </children>
                        </TreeItem>
                        <TreeItem value="إدارة الشحنات">
                           <children>
                              <TreeItem value="الشحنات" />
                              <TreeItem value="تفاصيل الشحنات" />
                              <TreeItem value="تتبع الشحنات" />
                           </children>
                        </TreeItem>
                        <TreeItem value="إدارة العملاء">
                           <children>
                              <TreeItem value="العملاء" />
                              <TreeItem value="فئات العملاء" />
                           </children>
                        </TreeItem>
                        <TreeItem value="إدارة الموردين">
                           <children>
                              <TreeItem value="الموردين" />
                              <TreeItem value="تقييم الموردين" />
                           </children>
                        </TreeItem>
                        <TreeItem value="إدارة المنتجات">
                           <children>
                              <TreeItem value="المنتجات" />
                              <TreeItem value="فئات المنتجات" />
                           </children>
                        </TreeItem>
                        <TreeItem value="إدارة المستندات">
                           <children>
                              <TreeItem value="المستندات" />
                              <TreeItem value="أنواع المستندات" />
                           </children>
                        </TreeItem>
                        <TreeItem value="التقارير">
                           <children>
                              <TreeItem value="تقارير الشحنات" />
                              <TreeItem value="تقارير العملاء" />
                              <TreeItem value="التقارير المالية" />
                           </children>
                        </TreeItem>
                     </children>
                  </TreeItem>
               </root>
            </TreeView>
         </VBox>

         <!-- منطقة المحتوى الرئيسي -->
         <!-- Main Content Area -->
         <TabPane fx:id="mainTabPane" styleClass="main-tab-pane" tabClosingPolicy="SELECTED_TAB">

            <!-- تبويب الترحيب -->
            <!-- Welcome Tab -->
            <Tab text="الصفحة الرئيسية" closable="false">
               <content>
                  <VBox alignment="CENTER" spacing="20" styleClass="welcome-content">
                     <padding>
                        <Insets bottom="40" left="40" right="40" top="40" />
                     </padding>

                     <!-- رسالة الترحيب -->
                     <!-- Welcome Message -->
                     <Label text="مرحباً بك في نظام إدارة الشحنات" styleClass="welcome-title">
                        <font>
                           <Font name="Tahoma Bold" size="24" />
                        </font>
                     </Label>

                     <Label text="نظام متكامل لإدارة الشحنات والخدمات اللوجستية"
                           styleClass="welcome-subtitle" textAlignment="CENTER">
                        <font>
                           <Font name="Tahoma" size="16" />
                        </font>
                     </Label>

                     <!-- إحصائيات سريعة -->
                     <!-- Quick Statistics -->
                     <GridPane alignment="CENTER" hgap="20" vgap="15" styleClass="stats-grid">
                        <columnConstraints>
                           <ColumnConstraints />
                           <ColumnConstraints />
                           <ColumnConstraints />
                           <ColumnConstraints />
                        </columnConstraints>

                        <!-- إجمالي الشحنات -->
                        <VBox alignment="CENTER" styleClass="stat-box" GridPane.columnIndex="0">
                           <Label text="0" styleClass="stat-number" />
                           <Label text="إجمالي الشحنات" styleClass="stat-label" />
                        </VBox>

                        <!-- الشحنات النشطة -->
                        <VBox alignment="CENTER" styleClass="stat-box" GridPane.columnIndex="1">
                           <Label text="0" styleClass="stat-number" />
                           <Label text="الشحنات النشطة" styleClass="stat-label" />
                        </VBox>

                        <!-- العملاء -->
                        <VBox alignment="CENTER" styleClass="stat-box" GridPane.columnIndex="2">
                           <Label text="0" styleClass="stat-number" />
                           <Label text="العملاء" styleClass="stat-label" />
                        </VBox>

                        <!-- الموردين -->
                        <VBox alignment="CENTER" styleClass="stat-box" GridPane.columnIndex="3">
                           <Label text="0" styleClass="stat-number" />
                           <Label text="الموردين" styleClass="stat-label" />
                        </VBox>
                     </GridPane>

                     <!-- أزرار سريعة -->
                     <!-- Quick Action Buttons -->
                     <HBox alignment="CENTER" spacing="15" styleClass="quick-actions">
                        <Button text="شحنة جديدة" onAction="#handleNewShipment"
                               styleClass="quick-action-button" prefWidth="120" />
                        <Button text="عميل جديد" onAction="#handleNewCustomer"
                               styleClass="quick-action-button" prefWidth="120" />
                        <Button text="مورد جديد" onAction="#handleNewSupplier"
                               styleClass="quick-action-button" prefWidth="120" />
                        <Button text="منتج جديد" onAction="#handleNewProduct"
                               styleClass="quick-action-button" prefWidth="120" />
                     </HBox>
                  </VBox>
               </content>
            </Tab>
         </TabPane>
      </SplitPane>
   </center>

   <!-- شريط الحالة -->
   <!-- Status Bar -->
   <bottom>
      <HBox fx:id="statusBar" alignment="CENTER_LEFT" spacing="10" styleClass="status-bar">
         <padding>
            <Insets bottom="5" left="10" right="10" top="5" />
         </padding>

         <!-- حالة النظام -->
         <!-- System Status -->
         <Label fx:id="statusLabel" text="جاهز" styleClass="status-label" />

         <Separator orientation="VERTICAL" />

         <!-- حالة قاعدة البيانات -->
         <!-- Database Status -->
         <Label fx:id="dbStatusLabel" text="متصل بقاعدة البيانات" styleClass="db-status-label" />

         <Separator orientation="VERTICAL" />

         <!-- عدد السجلات -->
         <!-- Record Count -->
         <Label fx:id="recordCountLabel" text="0 سجل" styleClass="record-count-label" />

         <!-- مساحة مرنة -->
         <!-- Flexible Space -->
         <Region HBox.hgrow="ALWAYS" />

         <!-- الوقت والتاريخ -->
         <!-- Date and Time -->
         <Label fx:id="dateTimeLabel" styleClass="datetime-label" />
      </HBox>
   </bottom>
</BorderPane>