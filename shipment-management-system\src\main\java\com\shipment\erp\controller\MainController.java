package com.shipment.erp.controller;

import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ResourceBundle;
import java.util.Timer;
import java.util.TimerTask;

/**
 * تحكم الواجهة الرئيسية
 * Main Interface Controller
 *
 * يدير الواجهة الرئيسية للنظام مع شجرة التنقل والتبويبات
 * Manages the main system interface with navigation tree and tabs
 */
public class MainController implements Initializable {

    private static final Logger logger = LoggerFactory.getLogger(MainController.class);

    // FXML Components
    @FXML private TreeView<String> navigationTree;
    @FXML private TabPane mainTabPane;
    @FXML private ToolBar mainToolBar;
    @FXML private Label userLabel;
    @FXML private Label statusLabel;
    @FXML private Label dbStatusLabel;
    @FXML private Label recordCountLabel;
    @FXML private Label dateTimeLabel;
    @FXML private CheckMenuItem toolbarMenuItem;
    @FXML private CheckMenuItem statusbarMenuItem;
    @FXML private CheckMenuItem sidebarMenuItem;

    // Timer for updating date/time
    private Timer dateTimeTimer;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.info("تهيئة الواجهة الرئيسية - Initializing main interface");

        try {
            setupNavigationTree();
            setupEventHandlers();
            updateDateTime();
            startDateTimeTimer();

            logger.info("تم تهيئة الواجهة الرئيسية بنجاح - Main interface initialized successfully");
        } catch (Exception e) {
            logger.error("خطأ في تهيئة الواجهة الرئيسية - Error initializing main interface", e);
        }
    }

    /**
     * إعداد شجرة التنقل
     * Setup navigation tree
     */
    private void setupNavigationTree() {
        if (navigationTree != null) {
            // إعداد معالج النقر المزدوج
            // Setup double-click handler
            navigationTree.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2) {
                    TreeItem<String> selectedItem = navigationTree.getSelectionModel().getSelectedItem();
                    if (selectedItem != null && selectedItem.isLeaf()) {
                        handleNavigationItemSelected(selectedItem.getValue());
                    }
                }
            });

            logger.info("تم إعداد شجرة التنقل - Navigation tree setup completed");
        }
    }

    /**
     * إعداد معالجات الأحداث
     * Setup event handlers
     */
    private void setupEventHandlers() {
        // معالجات القوائم
        // Menu handlers are defined in FXML with onAction attributes

        logger.info("تم إعداد معالجات الأحداث - Event handlers setup completed");
    }

    /**
     * تحديث التاريخ والوقت
     * Update date and time
     */
    private void updateDateTime() {
        if (dateTimeLabel != null) {
            Platform.runLater(() -> {
                String currentDateTime = LocalDateTime.now()
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                dateTimeLabel.setText(currentDateTime);
            });
        }
    }

    /**
     * بدء مؤقت التاريخ والوقت
     * Start date/time timer
     */
    private void startDateTimeTimer() {
        dateTimeTimer = new Timer(true);
        dateTimeTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                updateDateTime();
            }
        }, 0, 1000); // تحديث كل ثانية
    }

    /**
     * معالج اختيار عنصر من شجرة التنقل
     * Handle navigation item selection
     */
    private void handleNavigationItemSelected(String itemName) {
        logger.info("تم اختيار عنصر التنقل: {} - Navigation item selected: {}", itemName, itemName);

        try {
            switch (itemName) {
                case "المتغيرات العامة للبرنامج":
                    openSystemVariablesWindow();
                    break;
                case "إعداد السنة المالية":
                    openFiscalYearWindow();
                    break;
                case "تهيئة العملات":
                    openCurrencySetupWindow();
                    break;
                case "بيانات الشركة":
                    openCompanyDataWindow();
                    break;
                case "بيانات الفروع":
                    openBranchDataWindow();
                    break;
                case "بيانات المستخدمين":
                    openUserDataWindow();
                    break;
                case "صلاحيات المستخدمين":
                    openUserPermissionsWindow();
                    break;
                case "فتح سنة جديدة":
                    openNewYearWindow();
                    break;
                case "النسخ الاحتياطي والاستعادة":
                    openBackupRestoreWindow();
                    break;
                default:
                    showInfoAlert("قيد التطوير", "هذه الميزة قيد التطوير حالياً: " + itemName);
                    break;
            }
        } catch (Exception e) {
            logger.error("خطأ في فتح النافذة - Error opening window for: {}", itemName, e);
            showErrorAlert("خطأ", "حدث خطأ أثناء فتح النافذة: " + e.getMessage());
        }
    }

    // Window Opening Methods

    /**
     * فتح نافذة المتغيرات العامة للبرنامج
     * Open system variables window
     */
    private void openSystemVariablesWindow() throws IOException {
        openModalWindow("/fxml/system-variables.fxml", "المتغيرات العامة للبرنامج", 900, 700);
    }

    /**
     * فتح نافذة إعداد السنة المالية
     * Open fiscal year window
     */
    private void openFiscalYearWindow() throws IOException {
        showInfoAlert("قيد التطوير", "نافذة إعداد السنة المالية قيد التطوير");
    }

    /**
     * فتح نافذة تهيئة العملات
     * Open currency setup window
     */
    private void openCurrencySetupWindow() throws IOException {
        showInfoAlert("قيد التطوير", "نافذة تهيئة العملات قيد التطوير");
    }

    /**
     * فتح نافذة بيانات الشركة
     * Open company data window
     */
    private void openCompanyDataWindow() throws IOException {
        showInfoAlert("قيد التطوير", "نافذة بيانات الشركة قيد التطوير");
    }

    /**
     * فتح نافذة بيانات الفروع
     * Open branch data window
     */
    private void openBranchDataWindow() throws IOException {
        showInfoAlert("قيد التطوير", "نافذة بيانات الفروع قيد التطوير");
    }

    /**
     * فتح نافذة بيانات المستخدمين
     * Open user data window
     */
    private void openUserDataWindow() throws IOException {
        showInfoAlert("قيد التطوير", "نافذة بيانات المستخدمين قيد التطوير");
    }

    /**
     * فتح نافذة صلاحيات المستخدمين
     * Open user permissions window
     */
    private void openUserPermissionsWindow() throws IOException {
        showInfoAlert("قيد التطوير", "نافذة صلاحيات المستخدمين قيد التطوير");
    }

    /**
     * فتح نافذة فتح سنة جديدة
     * Open new year window
     */
    private void openNewYearWindow() throws IOException {
        showInfoAlert("قيد التطوير", "نافذة فتح سنة جديدة قيد التطوير");
    }

    /**
     * فتح نافذة النسخ الاحتياطي والاستعادة
     * Open backup and restore window
     */
    private void openBackupRestoreWindow() throws IOException {
        showInfoAlert("قيد التطوير", "نافذة النسخ الاحتياطي والاستعادة قيد التطوير");
    }

    /**
     * فتح نافذة مودال
     * Open modal window
     */
    private void openModalWindow(String fxmlPath, String title, double width, double height) throws IOException {
        FXMLLoader loader = new FXMLLoader(getClass().getResource(fxmlPath));
        Parent root = loader.load();

        Stage stage = new Stage();
        stage.setTitle(title);
        stage.initModality(Modality.APPLICATION_MODAL);
        stage.setScene(new Scene(root, width, height));
        stage.setResizable(true);

        // تطبيق ملف CSS إذا كان متوفراً
        // Apply CSS file if available
        try {
            String cssFile = getClass().getResource("/css/main.css").toExternalForm();
            stage.getScene().getStylesheets().add(cssFile);
        } catch (Exception e) {
            logger.warn("لم يتم العثور على ملف CSS - CSS file not found");
        }

        stage.showAndWait();
    }

    // Menu Event Handlers

    // File Menu
    @FXML private void handleNewFile() { showInfoAlert("قيد التطوير", "ميزة ملف جديد قيد التطوير"); }
    @FXML private void handleOpenFile() { showInfoAlert("قيد التطوير", "ميزة فتح ملف قيد التطوير"); }
    @FXML private void handleSaveFile() { showInfoAlert("قيد التطوير", "ميزة حفظ ملف قيد التطوير"); }
    @FXML private void handleSaveAsFile() { showInfoAlert("قيد التطوير", "ميزة حفظ باسم قيد التطوير"); }
    @FXML private void handlePrint() { showInfoAlert("قيد التطوير", "ميزة الطباعة قيد التطوير"); }
    @FXML private void handlePrintPreview() { showInfoAlert("قيد التطوير", "ميزة معاينة الطباعة قيد التطوير"); }
    @FXML private void handleExport() { showInfoAlert("قيد التطوير", "ميزة التصدير قيد التطوير"); }
    @FXML private void handleImport() { showInfoAlert("قيد التطوير", "ميزة الاستيراد قيد التطوير"); }
    @FXML private void handleExit() { Platform.exit(); }

    // Edit Menu
    @FXML private void handleUndo() { showInfoAlert("قيد التطوير", "ميزة التراجع قيد التطوير"); }
    @FXML private void handleRedo() { showInfoAlert("قيد التطوير", "ميزة الإعادة قيد التطوير"); }
    @FXML private void handleCut() { showInfoAlert("قيد التطوير", "ميزة القص قيد التطوير"); }
    @FXML private void handleCopy() { showInfoAlert("قيد التطوير", "ميزة النسخ قيد التطوير"); }
    @FXML private void handlePaste() { showInfoAlert("قيد التطوير", "ميزة اللصق قيد التطوير"); }
    @FXML private void handleDelete() { showInfoAlert("قيد التطوير", "ميزة الحذف قيد التطوير"); }
    @FXML private void handleSelectAll() { showInfoAlert("قيد التطوير", "ميزة تحديد الكل قيد التطوير"); }
    @FXML private void handleFind() { showInfoAlert("قيد التطوير", "ميزة البحث قيد التطوير"); }
    @FXML private void handleReplace() { showInfoAlert("قيد التطوير", "ميزة الاستبدال قيد التطوير"); }
    @FXML private void handlePreferences() { showInfoAlert("قيد التطوير", "ميزة التفضيلات قيد التطوير"); }

    // View Menu
    @FXML private void handleToggleToolbar() {
        if (mainToolBar != null) {
            mainToolBar.setVisible(toolbarMenuItem.isSelected());
        }
    }

    @FXML private void handleToggleStatusbar() {
        // Status bar toggle logic
        showInfoAlert("قيد التطوير", "تبديل شريط الحالة قيد التطوير");
    }

    @FXML private void handleToggleSidebar() {
        // Sidebar toggle logic
        showInfoAlert("قيد التطوير", "تبديل الشريط الجانبي قيد التطوير");
    }

    @FXML private void handleFullscreen() { showInfoAlert("قيد التطوير", "ميزة ملء الشاشة قيد التطوير"); }
    @FXML private void handleZoomIn() { showInfoAlert("قيد التطوير", "ميزة التكبير قيد التطوير"); }
    @FXML private void handleZoomOut() { showInfoAlert("قيد التطوير", "ميزة التصغير قيد التطوير"); }
    @FXML private void handleZoomReset() { showInfoAlert("قيد التطوير", "ميزة إعادة تعيين التكبير قيد التطوير"); }
    @FXML private void handleRefresh() { showInfoAlert("قيد التطوير", "ميزة التحديث قيد التطوير"); }

    // Tools Menu
    @FXML private void handleCalculator() { showInfoAlert("قيد التطوير", "ميزة الآلة الحاسبة قيد التطوير"); }
    @FXML private void handleCalendar() { showInfoAlert("قيد التطوير", "ميزة التقويم قيد التطوير"); }
    @FXML private void handleReports() { showInfoAlert("قيد التطوير", "ميزة التقارير قيد التطوير"); }
    @FXML private void handleBackup() { showInfoAlert("قيد التطوير", "ميزة النسخ الاحتياطي قيد التطوير"); }
    @FXML private void handleRestore() { showInfoAlert("قيد التطوير", "ميزة الاستعادة قيد التطوير"); }
    @FXML private void handleSettings() {
        try {
            openSystemVariablesWindow();
        } catch (IOException e) {
            showErrorAlert("خطأ", "حدث خطأ أثناء فتح الإعدادات: " + e.getMessage());
        }
    }

    // Window Menu
    @FXML private void handleSwitchWindows() { showInfoAlert("قيد التطوير", "ميزة تبديل النوافذ قيد التطوير"); }
    @FXML private void handleCloseCurrentWindow() { showInfoAlert("قيد التطوير", "ميزة إغلاق النافذة الحالية قيد التطوير"); }
    @FXML private void handleCloseAllWindows() { showInfoAlert("قيد التطوير", "ميزة إغلاق جميع النوافذ قيد التطوير"); }
    @FXML private void handleArrangeWindows() { showInfoAlert("قيد التطوير", "ميزة ترتيب النوافذ قيد التطوير"); }

    // Help Menu
    @FXML private void handleUserManual() { showInfoAlert("قيد التطوير", "ميزة دليل المستخدم قيد التطوير"); }
    @FXML private void handleShortcuts() { showInfoAlert("قيد التطوير", "ميزة اختصارات لوحة المفاتيح قيد التطوير"); }
    @FXML private void handleContact() { showInfoAlert("قيد التطوير", "ميزة اتصل بنا قيد التطوير"); }
    @FXML private void handleUpdates() { showInfoAlert("قيد التطوير", "ميزة التحديثات قيد التطوير"); }
    @FXML private void handleAbout() {
        showInfoAlert("حول البرنامج",
            "نظام إدارة الشحنات\nالإصدار 1.0.0\n\nتم التطوير بواسطة فريق التطوير\n2025");
    }

    // Toolbar Event Handlers
    @FXML private void handleNewRecord() { showInfoAlert("قيد التطوير", "ميزة إضافة سجل جديد قيد التطوير"); }
    @FXML private void handleEditRecord() { showInfoAlert("قيد التطوير", "ميزة تعديل السجل قيد التطوير"); }
    @FXML private void handleDeleteRecord() { showInfoAlert("قيد التطوير", "ميزة حذف السجل قيد التطوير"); }
    @FXML private void handleSearch() { showInfoAlert("قيد التطوير", "ميزة البحث قيد التطوير"); }
    @FXML private void handleFilter() { showInfoAlert("قيد التطوير", "ميزة التصفية قيد التطوير"); }
    @FXML private void handleRefreshData() { showInfoAlert("قيد التطوير", "ميزة تحديث البيانات قيد التطوير"); }
    @FXML private void handlePrintReport() { showInfoAlert("قيد التطوير", "ميزة طباعة التقرير قيد التطوير"); }
    @FXML private void handleExportData() { showInfoAlert("قيد التطوير", "ميزة تصدير البيانات قيد التطوير"); }
    @FXML private void handleLogout() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("تأكيد تسجيل الخروج");
        alert.setHeaderText("هل تريد تسجيل الخروج من النظام؟");
        alert.setContentText("سيتم إغلاق جميع النوافذ المفتوحة.");

        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                Platform.exit();
            }
        });
    }

    // Quick Action Handlers
    @FXML private void handleNewShipment() { showInfoAlert("قيد التطوير", "ميزة شحنة جديدة قيد التطوير"); }
    @FXML private void handleNewCustomer() { showInfoAlert("قيد التطوير", "ميزة عميل جديد قيد التطوير"); }
    @FXML private void handleNewSupplier() { showInfoAlert("قيد التطوير", "ميزة مورد جديد قيد التطوير"); }
    @FXML private void handleNewProduct() { showInfoAlert("قيد التطوير", "ميزة منتج جديد قيد التطوير"); }

    // Alert Helper Methods

    /**
     * عرض تنبيه معلومات
     * Show info alert
     */
    private void showInfoAlert(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    /**
     * عرض تنبيه خطأ
     * Show error alert
     */
    private void showErrorAlert(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    /**
     * عرض تنبيه تحذير
     * Show warning alert
     */
    private void showWarningAlert(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.WARNING);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    /**
     * تنظيف الموارد عند إغلاق النافذة
     * Cleanup resources when closing window
     */
    public void cleanup() {
        if (dateTimeTimer != null) {
            dateTimeTimer.cancel();
            dateTimeTimer = null;
        }
    }
}