package com.shipment.erp.controller;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ResourceBundle;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.shipment.erp.util.AlertUtil;

import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.CheckMenuItem;
import javafx.scene.control.Label;
import javafx.scene.control.Tab;
import javafx.scene.control.TabPane;
import javafx.scene.control.ToolBar;
import javafx.scene.control.TreeItem;
import javafx.scene.control.TreeView;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.util.Duration;

/**
 * تحكم الشاشة الرئيسية
 * Main Screen Controller
 * 
 * يدير الشاشة الرئيسية والتنقل بين الوحدات
 * Manages main screen and navigation between modules
 * 
 * <AUTHOR> إدارة الشحنات
 * @version 1.0.0
 */
public class MainController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(MainController.class);
    
    // عناصر واجهة المستخدم
    // UI Components
    @FXML private ToolBar mainToolBar;
    @FXML private VBox sidebar;
    @FXML private TreeView<String> navigationTree;
    @FXML private TabPane mainTabPane;
    @FXML private HBox statusBar;
    @FXML private Label statusLabel;
    @FXML private Label dbStatusLabel;
    @FXML private Label recordCountLabel;
    @FXML private Label dateTimeLabel;
    @FXML private Label userLabel;
    
    // عناصر القوائم
    // Menu Items
    @FXML private CheckMenuItem toolbarMenuItem;
    @FXML private CheckMenuItem statusbarMenuItem;
    @FXML private CheckMenuItem sidebarMenuItem;
    
    private ResourceBundle resourceBundle;
    private String currentUser = "admin";
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        this.resourceBundle = resources;
        
        logger.info("تهيئة الشاشة الرئيسية - Initializing main screen");
        
        // إعداد واجهة المستخدم
        // Setup UI
        setupUI();
        
        // إعداد شجرة التنقل
        // Setup navigation tree
        setupNavigationTree();
        
        // بدء مؤقت التاريخ والوقت
        // Start date/time timer
        startDateTimeUpdater();
        
        // تحديث شريط الحالة
        // Update status bar
        updateStatusBar();
        
        logger.info("تم تهيئة الشاشة الرئيسية بنجاح - Main screen initialized successfully");
    }
    
    /**
     * إعداد واجهة المستخدم
     * Setup user interface
     */
    private void setupUI() {
        // تعيين معلومات المستخدم
        // Set user information
        if (userLabel != null) {
            userLabel.setText("المستخدم: " + currentUser);
        }
        
        // تعيين حالة قاعدة البيانات
        // Set database status
        if (dbStatusLabel != null) {
            dbStatusLabel.setText("متصل بقاعدة البيانات");
            dbStatusLabel.getStyleClass().add("db-connected");
        }
        
        // تعيين عدد السجلات
        // Set record count
        if (recordCountLabel != null) {
            recordCountLabel.setText("0 سجل");
        }
    }
    
    /**
     * إعداد شجرة التنقل
     * Setup navigation tree
     */
    private void setupNavigationTree() {
        if (navigationTree != null) {
            // إعداد معالج النقر على العقد
            // Setup node click handler
            navigationTree.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2) {
                    TreeItem<String> selectedItem = navigationTree.getSelectionModel().getSelectedItem();
                    if (selectedItem != null && selectedItem.isLeaf()) {
                        handleNavigationSelection(selectedItem.getValue());
                    }
                }
            });
        }
    }
    
    /**
     * معالج اختيار عنصر من شجرة التنقل
     * Handle navigation tree selection
     */
    private void handleNavigationSelection(String itemName) {
        logger.info("تم اختيار عنصر التنقل: {} - Navigation item selected: {}", itemName, itemName);
        
        switch (itemName) {
            case "الشحنات":
                openShipmentsModule();
                break;
            case "العملاء":
                openCustomersModule();
                break;
            case "الموردين":
                openSuppliersModule();
                break;
            case "المنتجات":
                openProductsModule();
                break;
            case "المستندات":
                openDocumentsModule();
                break;
            case "المستخدمين":
                openUsersModule();
                break;
            case "الإعدادات":
                openSettingsModule();
                break;
            default:
                AlertUtil.showInfo("قريباً", "هذه الوحدة قيد التطوير وستكون متاحة قريباً.");
                break;
        }
    }
    
    /**
     * فتح وحدة الشحنات
     * Open shipments module
     */
    private void openShipmentsModule() {
        Tab shipmentsTab = findOrCreateTab("الشحنات", "shipments");
        if (shipmentsTab != null) {
            mainTabPane.getSelectionModel().select(shipmentsTab);
        }
    }
    
    /**
     * فتح وحدة العملاء
     * Open customers module
     */
    private void openCustomersModule() {
        Tab customersTab = findOrCreateTab("العملاء", "customers");
        if (customersTab != null) {
            mainTabPane.getSelectionModel().select(customersTab);
        }
    }
    
    /**
     * فتح وحدة الموردين
     * Open suppliers module
     */
    private void openSuppliersModule() {
        Tab suppliersTab = findOrCreateTab("الموردين", "suppliers");
        if (suppliersTab != null) {
            mainTabPane.getSelectionModel().select(suppliersTab);
        }
    }
    
    /**
     * فتح وحدة المنتجات
     * Open products module
     */
    private void openProductsModule() {
        Tab productsTab = findOrCreateTab("المنتجات", "products");
        if (productsTab != null) {
            mainTabPane.getSelectionModel().select(productsTab);
        }
    }
    
    /**
     * فتح وحدة المستندات
     * Open documents module
     */
    private void openDocumentsModule() {
        Tab documentsTab = findOrCreateTab("المستندات", "documents");
        if (documentsTab != null) {
            mainTabPane.getSelectionModel().select(documentsTab);
        }
    }
    
    /**
     * فتح وحدة المستخدمين
     * Open users module
     */
    private void openUsersModule() {
        Tab usersTab = findOrCreateTab("المستخدمين", "users");
        if (usersTab != null) {
            mainTabPane.getSelectionModel().select(usersTab);
        }
    }
    
    /**
     * فتح وحدة الإعدادات
     * Open settings module
     */
    private void openSettingsModule() {
        Tab settingsTab = findOrCreateTab("الإعدادات", "settings");
        if (settingsTab != null) {
            mainTabPane.getSelectionModel().select(settingsTab);
        }
    }
    
    /**
     * البحث عن تبويب أو إنشاء جديد
     * Find existing tab or create new one
     */
    private Tab findOrCreateTab(String title, String id) {
        // البحث عن تبويب موجود
        // Search for existing tab
        for (Tab tab : mainTabPane.getTabs()) {
            if (title.equals(tab.getText())) {
                return tab;
            }
        }
        
        // إنشاء تبويب جديد
        // Create new tab
        Tab newTab = new Tab(title);
        newTab.setId(id);
        newTab.setClosable(true);
        
        // إنشاء محتوى مؤقت
        // Create temporary content
        VBox content = new VBox();
        content.getChildren().add(new Label("وحدة " + title + " قيد التطوير"));
        content.setStyle("-fx-padding: 20; -fx-alignment: center;");
        
        newTab.setContent(content);
        mainTabPane.getTabs().add(newTab);
        
        logger.info("تم إنشاء تبويب جديد: {} - Created new tab: {}", title, title);
        return newTab;
    }
    
    /**
     * بدء مؤقت تحديث التاريخ والوقت
     * Start date/time updater timer
     */
    private void startDateTimeUpdater() {
        if (dateTimeLabel != null) {
            // تحديث فوري
            // Immediate update
            updateDateTime();
            
            // تحديث كل ثانية
            // Update every second
            Timeline timeline = new Timeline(
                new KeyFrame(Duration.seconds(1), e -> updateDateTime())
            );
            timeline.setCycleCount(Timeline.INDEFINITE);
            timeline.play();
        }
    }
    
    /**
     * تحديث التاريخ والوقت
     * Update date and time
     */
    private void updateDateTime() {
        if (dateTimeLabel != null) {
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            dateTimeLabel.setText(now.format(formatter));
        }
    }
    
    /**
     * تحديث شريط الحالة
     * Update status bar
     */
    private void updateStatusBar() {
        if (statusLabel != null) {
            statusLabel.setText("جاهز");
        }
    }
    
    // ===== معالجات القوائم =====
    // ===== Menu Handlers =====
    
    @FXML private void handleNewFile() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleOpenFile() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleSaveFile() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleSaveAsFile() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handlePrint() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handlePrintPreview() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleExport() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleImport() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleExit() { 
        boolean confirmed = AlertUtil.showConfirmation("تأكيد الخروج", "هل أنت متأكد من الخروج من النظام؟");
        if (confirmed) {
            Platform.exit();
        }
    }
    
    @FXML private void handleUndo() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleRedo() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleCut() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleCopy() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handlePaste() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleDelete() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleSelectAll() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleFind() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleReplace() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handlePreferences() { openSettingsModule(); }
    
    @FXML private void handleToggleToolbar() {
        if (mainToolBar != null) {
            mainToolBar.setVisible(toolbarMenuItem.isSelected());
        }
    }
    
    @FXML private void handleToggleStatusbar() {
        if (statusBar != null) {
            statusBar.setVisible(statusbarMenuItem.isSelected());
        }
    }
    
    @FXML private void handleToggleSidebar() {
        if (sidebar != null) {
            sidebar.setVisible(sidebarMenuItem.isSelected());
        }
    }
    
    @FXML private void handleFullscreen() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleZoomIn() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleZoomOut() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleZoomReset() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleRefresh() { 
        updateStatusBar();
        AlertUtil.showInfo("تحديث", "تم تحديث البيانات.");
    }
    
    // ===== معالجات شريط الأدوات =====
    // ===== Toolbar Handlers =====
    
    @FXML private void handleNewRecord() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleEditRecord() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleDeleteRecord() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleSearch() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleFilter() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleRefreshData() { 
        updateStatusBar();
        AlertUtil.showInfo("تحديث", "تم تحديث البيانات.");
    }
    @FXML private void handlePrintReport() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleExportData() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    
    @FXML private void handleLogout() {
        boolean confirmed = AlertUtil.showConfirmation("تأكيد تسجيل الخروج", 
            "هل أنت متأكد من تسجيل الخروج؟ سيتم إغلاق جميع النوافذ المفتوحة.");
        if (confirmed) {
            logger.info("تسجيل خروج المستخدم: {} - User logout: {}", currentUser, currentUser);
            Platform.exit();
        }
    }
    
    // ===== معالجات الأزرار السريعة =====
    // ===== Quick Action Handlers =====
    
    @FXML private void handleNewShipment() { openShipmentsModule(); }
    @FXML private void handleNewCustomer() { openCustomersModule(); }
    @FXML private void handleNewSupplier() { openSuppliersModule(); }
    @FXML private void handleNewProduct() { openProductsModule(); }
    
    // ===== معالجات أخرى =====
    // ===== Other Handlers =====
    
    @FXML private void handleCalculator() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleCalendar() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleReports() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleBackup() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleRestore() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleSettings() { openSettingsModule(); }
    @FXML private void handleSwitchWindows() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleCloseCurrentWindow() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleCloseAllWindows() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleArrangeWindows() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleUserManual() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleShortcuts() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleContact() { 
        AlertUtil.showInfo("اتصل بنا", 
            "للدعم الفني والاستفسارات:\n\n" +
            "الهاتف: +20-2-12345678\n" +
            "البريد الإلكتروني: <EMAIL>\n" +
            "الموقع الإلكتروني: www.shipment-erp.com");
    }
    @FXML private void handleUpdates() { AlertUtil.showInfo("قريباً", "هذه الميزة قيد التطوير."); }
    @FXML private void handleAbout() {
        AlertUtil.showInfo("حول البرنامج", 
            "نظام إدارة الشحنات\n" +
            "الإصدار 1.0.0\n\n" +
            "نظام متكامل لإدارة الشحنات والخدمات اللوجستية\n" +
            "يدعم اللغة العربية مع قاعدة بيانات Oracle\n\n" +
            "جميع الحقوق محفوظة © 2025");
    }
}
